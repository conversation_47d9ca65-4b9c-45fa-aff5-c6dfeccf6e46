# Environment Configuration
NODE_ENV=development
PORT=3000

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

# Weaviate Configuration
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=redispassword

# MongoDB Configuration
MONGODB_URI=*********************************************************************

# AI API Keys
GEMINI_API_KEY=yAIzaSyAtdDxONckBeZ_4ftbHi-UcpUhkRnXy-is
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Ollama Configuration (optional)
OLLAMA_URL=http://localhost:11434

# Research API Keys
BRAVE_API_KEY=your_brave_search_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here

# Medical AI Configuration
GEMMA_MEDICAL_URL=http://*************:1234

# Security
JWT_SECRET=Tc1tN3lJYULQ0Mdh6_YXou7XuMhIqSXWkn_eILH7oHc
JWT_EXPIRES_IN=7d

# CORS
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# File Upload
MAX_FILE_SIZE=52428800
UPLOAD_DIR=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Cache TTL (Time To Live) in seconds
CACHE_TTL=3600

# AI Model Configuration
DEFAULT_AI_MODEL=gemini-pro
MAX_TOKENS=4096
TEMPERATURE=0.7

# Knowledge Graph Configuration
MAX_GRAPH_NODES=10000
MAX_GRAPH_RELATIONSHIPS=50000
GRAPH_EXPANSION_LIMIT=100

# RAG Configuration
EMBEDDING_MODEL=text-embedding-ada-002
VECTOR_DIMENSION=1536
SIMILARITY_THRESHOLD=0.7
MAX_CONTEXT_LENGTH=8000

# Crawling Configuration
CRAWL_DELAY_MS=1000
MAX_CRAWL_DEPTH=3
MAX_PAGES_PER_DOMAIN=100

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
