import weaviate, { <PERSON>aviate<PERSON>lient, A<PERSON><PERSON><PERSON> } from 'weaviate-ts-client';
import { config } from '@/config/environment';
import { logger, logDatabaseOperation, logError } from '@/utils/logger';

class WeaviateConnection {
  private client: WeaviateClient | null = null;
  private isConnected = false;

  async connect(): Promise<void> {
    try {
      logger.info('Connecting to Weaviate vector database...');
      
      // Create client configuration
      const clientConfig: any = {
        scheme: config.weaviate.url.startsWith('https') ? 'https' : 'http',
        host: config.weaviate.url.replace(/^https?:\/\//, ''),
      };

      // Add API key if provided
      if (config.weaviate.apiKey) {
        clientConfig.apiKey = new ApiKey(config.weaviate.apiKey);
      }

      this.client = weaviate.client(clientConfig);

      // Verify connectivity
      await this.healthCheck();
      this.isConnected = true;
      
      logger.info('✅ Weaviate connection established successfully');
      
      // Initialize schemas
      await this.initializeSchemas();
      
    } catch (error) {
      logError('❌ Failed to connect to Weaviate', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      this.isConnected = false;
      this.client = null;
      logger.info('🔌 Weaviate connection closed');
    }
  }

  getClient(): WeaviateClient {
    if (!this.client || !this.isConnected) {
      throw new Error('Weaviate client not initialized. Call connect() first.');
    }
    return this.client;
  }

  private async initializeSchemas(): Promise<void> {
    try {
      logger.info('Initializing Weaviate schemas...');
      
      const client = this.getClient();
      
      // Define schemas for different entity types
      const schemas = [
        {
          class: 'Supplement',
          description: 'Supplement products and their information',
          properties: [
            {
              name: 'name',
              dataType: ['text'],
              description: 'Name of the supplement',
            },
            {
              name: 'description',
              dataType: ['text'],
              description: 'Description of the supplement',
            },
            {
              name: 'brand',
              dataType: ['text'],
              description: 'Brand or manufacturer',
            },
            {
              name: 'category',
              dataType: ['text'],
              description: 'Category of supplement',
            },
            {
              name: 'dosage',
              dataType: ['text'],
              description: 'Recommended dosage information',
            },
            {
              name: 'ingredients',
              dataType: ['text[]'],
              description: 'List of ingredients',
            },
            {
              name: 'benefits',
              dataType: ['text[]'],
              description: 'Claimed benefits',
            },
            {
              name: 'sideEffects',
              dataType: ['text[]'],
              description: 'Potential side effects',
            },
            {
              name: 'contraindications',
              dataType: ['text[]'],
              description: 'Contraindications and warnings',
            },
            {
              name: 'sourceUrl',
              dataType: ['text'],
              description: 'Source URL for the information',
            },
            {
              name: 'lastUpdated',
              dataType: ['date'],
              description: 'Last update timestamp',
            },
          ],
          vectorizer: 'text2vec-openai',
          moduleConfig: {
            'text2vec-openai': {
              model: 'ada',
              modelVersion: '002',
              type: 'text',
            },
          },
        },
        {
          class: 'Ingredient',
          description: 'Individual ingredients found in supplements',
          properties: [
            {
              name: 'name',
              dataType: ['text'],
              description: 'Name of the ingredient',
            },
            {
              name: 'chemicalName',
              dataType: ['text'],
              description: 'Chemical or scientific name',
            },
            {
              name: 'description',
              dataType: ['text'],
              description: 'Description of the ingredient',
            },
            {
              name: 'molecularFormula',
              dataType: ['text'],
              description: 'Molecular formula',
            },
            {
              name: 'casNumber',
              dataType: ['text'],
              description: 'CAS registry number',
            },
            {
              name: 'functions',
              dataType: ['text[]'],
              description: 'Biological functions',
            },
            {
              name: 'sources',
              dataType: ['text[]'],
              description: 'Natural sources',
            },
            {
              name: 'safetyProfile',
              dataType: ['text'],
              description: 'Safety information',
            },
            {
              name: 'interactions',
              dataType: ['text[]'],
              description: 'Known interactions',
            },
          ],
          vectorizer: 'text2vec-openai',
          moduleConfig: {
            'text2vec-openai': {
              model: 'ada',
              modelVersion: '002',
              type: 'text',
            },
          },
        },
        {
          class: 'Study',
          description: 'Scientific studies and research papers',
          properties: [
            {
              name: 'title',
              dataType: ['text'],
              description: 'Title of the study',
            },
            {
              name: 'abstract',
              dataType: ['text'],
              description: 'Study abstract',
            },
            {
              name: 'authors',
              dataType: ['text[]'],
              description: 'Study authors',
            },
            {
              name: 'journal',
              dataType: ['text'],
              description: 'Journal name',
            },
            {
              name: 'publicationDate',
              dataType: ['date'],
              description: 'Publication date',
            },
            {
              name: 'doi',
              dataType: ['text'],
              description: 'Digital Object Identifier',
            },
            {
              name: 'pmid',
              dataType: ['text'],
              description: 'PubMed ID',
            },
            {
              name: 'studyType',
              dataType: ['text'],
              description: 'Type of study (RCT, observational, etc.)',
            },
            {
              name: 'participants',
              dataType: ['int'],
              description: 'Number of participants',
            },
            {
              name: 'conclusions',
              dataType: ['text'],
              description: 'Study conclusions',
            },
            {
              name: 'qualityScore',
              dataType: ['number'],
              description: 'Quality assessment score',
            },
          ],
          vectorizer: 'text2vec-openai',
          moduleConfig: {
            'text2vec-openai': {
              model: 'ada',
              modelVersion: '002',
              type: 'text',
            },
          },
        },
        {
          class: 'Effect',
          description: 'Effects and benefits of supplements',
          properties: [
            {
              name: 'name',
              dataType: ['text'],
              description: 'Name of the effect',
            },
            {
              name: 'description',
              dataType: ['text'],
              description: 'Description of the effect',
            },
            {
              name: 'category',
              dataType: ['text'],
              description: 'Category of effect (cognitive, physical, etc.)',
            },
            {
              name: 'mechanism',
              dataType: ['text'],
              description: 'Mechanism of action',
            },
            {
              name: 'evidenceLevel',
              dataType: ['text'],
              description: 'Level of scientific evidence',
            },
            {
              name: 'timeToEffect',
              dataType: ['text'],
              description: 'Time to see effect',
            },
            {
              name: 'duration',
              dataType: ['text'],
              description: 'Duration of effect',
            },
          ],
          vectorizer: 'text2vec-openai',
          moduleConfig: {
            'text2vec-openai': {
              model: 'ada',
              modelVersion: '002',
              type: 'text',
            },
          },
        },
      ];

      // Create schemas if they don't exist
      for (const schema of schemas) {
        try {
          const exists = await client.schema.exists(schema.class);
          if (!exists) {
            await client.schema.classCreator().withClass(schema).do();
            logger.info(`✅ Created Weaviate schema: ${schema.class}`);
          } else {
            logger.info(`ℹ️ Weaviate schema already exists: ${schema.class}`);
          }
        } catch (error) {
          logError(`Failed to create schema: ${schema.class}`, error);
          throw error;
        }
      }
      
      logger.info('✅ Weaviate schemas initialized successfully');
    } catch (error) {
      logError('❌ Failed to initialize Weaviate schemas', error);
      throw error;
    }
  }

  async addObject(className: string, properties: any, id?: string): Promise<string> {
    const client = this.getClient();
    const startTime = Date.now();
    
    try {
      let creator = client.data.creator().withClassName(className).withProperties(properties);
      
      if (id) {
        creator = creator.withId(id);
      }
      
      const result = await creator.do();
      const duration = Date.now() - startTime;
      
      logDatabaseOperation('Object added', `Weaviate:${className}`, duration, {
        objectId: result.id,
      });
      
      return result.id;
    } catch (error) {
      logError(`Failed to add object to ${className}`, error, { properties });
      throw error;
    }
  }

  async searchSimilar(className: string, query: string, limit: number = 10): Promise<any[]> {
    const client = this.getClient();
    const startTime = Date.now();
    
    try {
      const result = await client.graphql
        .get()
        .withClassName(className)
        .withNearText({ concepts: [query] })
        .withLimit(limit)
        .withFields('_additional { certainty distance } name description')
        .do();
      
      const duration = Date.now() - startTime;
      logDatabaseOperation('Similarity search', `Weaviate:${className}`, duration, {
        query,
        resultCount: result.data.Get[className]?.length || 0,
      });
      
      return result.data.Get[className] || [];
    } catch (error) {
      logError(`Failed to search ${className}`, error, { query });
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const client = this.getClient();
      const result = await client.misc.liveChecker().do();
      return result === true;
    } catch (error) {
      logError('Weaviate health check failed', error);
      return false;
    }
  }

  async getStats(): Promise<any> {
    try {
      const client = this.getClient();
      const schema = await client.schema.getter().do();
      
      const stats: any = {
        classes: schema.classes?.length || 0,
        classDetails: [],
      };

      if (schema.classes) {
        for (const cls of schema.classes) {
          try {
            const result = await client.graphql
              .aggregate()
              .withClassName(cls.class)
              .withFields('meta { count }')
              .do();
            
            stats.classDetails.push({
              name: cls.class,
              count: result.data.Aggregate[cls.class]?.[0]?.meta?.count || 0,
            });
          } catch (error) {
            // Ignore errors for individual class stats
            stats.classDetails.push({
              name: cls.class,
              count: 0,
              error: 'Failed to get count',
            });
          }
        }
      }

      return stats;
    } catch (error) {
      logError('Failed to get Weaviate stats', error);
      throw error;
    }
  }

  // Helper method to clear all data (use with caution!)
  async clearDatabase(): Promise<void> {
    if (config.isProduction) {
      throw new Error('Cannot clear database in production environment');
    }
    
    try {
      const client = this.getClient();
      const schema = await client.schema.getter().do();
      
      if (schema.classes) {
        for (const cls of schema.classes) {
          await client.schema.classDeleter().withClassName(cls.class).do();
        }
      }
      
      logger.warn('⚠️ Weaviate database cleared');
    } catch (error) {
      logError('Failed to clear Weaviate database', error);
      throw error;
    }
  }
}

// Create singleton instance
const weaviateConnection = new WeaviateConnection();

// Export connection functions
export const connectWeaviate = () => weaviateConnection.connect();
export const disconnectWeaviate = () => weaviateConnection.disconnect();
export const getWeaviateClient = () => weaviateConnection.getClient();
export const addWeaviateObject = (className: string, properties: any, id?: string) =>
  weaviateConnection.addObject(className, properties, id);
export const searchWeaviateSimilar = (className: string, query: string, limit?: number) =>
  weaviateConnection.searchSimilar(className, query, limit);
export const weaviateHealthCheck = () => weaviateConnection.healthCheck();
export const getWeaviateStats = () => weaviateConnection.getStats();
export const clearWeaviateDatabase = () => weaviateConnection.clearDatabase();

export default weaviateConnection;
