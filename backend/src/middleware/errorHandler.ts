import { Request, Response, NextFunction } from 'express';
import { config } from '@/config/environment';
import { logger, logError, logSecurityEvent } from '@/utils/logger';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// Error types
export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
    this.code = 'VALIDATION_ERROR';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401);
    this.code = 'AUTHENTICATION_ERROR';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403);
    this.code = 'AUTHORIZATION_ERROR';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
    this.code = 'NOT_FOUND_ERROR';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409);
    this.code = 'CONFLICT_ERROR';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429);
    this.code = 'RATE_LIMIT_ERROR';
  }
}

export class DatabaseError extends AppError {
  constructor(message: string = 'Database operation failed') {
    super(message, 500);
    this.code = 'DATABASE_ERROR';
  }
}

export class ExternalServiceError extends AppError {
  constructor(message: string = 'External service error') {
    super(message, 502);
    this.code = 'EXTERNAL_SERVICE_ERROR';
  }
}

// Error response interface
interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    statusCode: number;
    timestamp: string;
    path: string;
    method: string;
    stack?: string;
    details?: any;
  };
}

// Handle different error types
const handleCastErrorDB = (err: any): AppError => {
  const message = `Invalid ${err.path}: ${err.value}`;
  return new ValidationError(message);
};

const handleDuplicateFieldsDB = (err: any): AppError => {
  const value = err.errmsg?.match(/(["'])(\\?.)*?\1/)?.[0];
  const message = `Duplicate field value: ${value}. Please use another value!`;
  return new ConflictError(message);
};

const handleValidationErrorDB = (err: any): AppError => {
  const errors = Object.values(err.errors).map((el: any) => el.message);
  const message = `Invalid input data. ${errors.join('. ')}`;
  return new ValidationError(message);
};

const handleJWTError = (): AppError => {
  return new AuthenticationError('Invalid token. Please log in again!');
};

const handleJWTExpiredError = (): AppError => {
  return new AuthenticationError('Your token has expired! Please log in again.');
};

const handleNeo4jError = (err: any): AppError => {
  if (err.code === 'Neo.ClientError.Statement.SyntaxError') {
    return new ValidationError('Invalid query syntax');
  }
  if (err.code === 'Neo.TransientError.General.DatabaseUnavailable') {
    return new DatabaseError('Database temporarily unavailable');
  }
  return new DatabaseError('Database operation failed');
};

const handleWeaviateError = (err: any): AppError => {
  if (err.message?.includes('unauthorized')) {
    return new AuthenticationError('Vector database authentication failed');
  }
  if (err.message?.includes('not found')) {
    return new NotFoundError('Vector database resource not found');
  }
  return new ExternalServiceError('Vector database operation failed');
};

const handleRedisError = (err: any): AppError => {
  if (err.code === 'ECONNREFUSED') {
    return new ExternalServiceError('Cache service unavailable');
  }
  return new ExternalServiceError('Cache operation failed');
};

// Send error response
const sendErrorDev = (err: AppError, req: Request, res: Response): void => {
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      message: err.message,
      code: err.code,
      statusCode: err.statusCode,
      timestamp: new Date().toISOString(),
      path: req.originalUrl,
      method: req.method,
      stack: err.stack,
      details: err,
    },
  };

  res.status(err.statusCode).json(errorResponse);
};

const sendErrorProd = (err: AppError, req: Request, res: Response): void => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        message: err.message,
        code: err.code,
        statusCode: err.statusCode,
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        method: req.method,
      },
    };

    res.status(err.statusCode).json(errorResponse);
  } else {
    // Programming or other unknown error: don't leak error details
    logError('Unknown error occurred', err, {
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        message: 'Something went wrong!',
        statusCode: 500,
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        method: req.method,
      },
    };

    res.status(500).json(errorResponse);
  }
};

// Main error handling middleware
export const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  const errorContext = {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
  };

  // Log security events for certain error types
  if (err.statusCode === 401 || err.statusCode === 403) {
    logSecurityEvent(
      `${err.statusCode === 401 ? 'Authentication' : 'Authorization'} failure`,
      'medium',
      errorContext
    );
  }

  if (err.statusCode === 429) {
    logSecurityEvent('Rate limit exceeded', 'low', errorContext);
  }

  // Handle specific error types
  if (err.name === 'CastError') error = handleCastErrorDB(err);
  if (err.code === 11000) error = handleDuplicateFieldsDB(err);
  if (err.name === 'ValidationError') error = handleValidationErrorDB(err);
  if (err.name === 'JsonWebTokenError') error = handleJWTError();
  if (err.name === 'TokenExpiredError') error = handleJWTExpiredError();
  
  // Handle database-specific errors
  if (err.code?.startsWith('Neo.')) error = handleNeo4jError(err);
  if (err.name === 'WeaviateError') error = handleWeaviateError(err);
  if (err.code === 'ECONNREFUSED' && err.syscall === 'connect') {
    error = handleRedisError(err);
  }

  // Ensure error is an AppError instance
  if (!(error instanceof AppError)) {
    error = new AppError(error.message || 'Something went wrong!', error.statusCode || 500);
  }

  // Send error response
  if (config.isDevelopment) {
    sendErrorDev(error, req, res);
  } else {
    sendErrorProd(error, req, res);
  }
};

// Async error wrapper
export const catchAsync = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    fn(req, res, next).catch(next);
  };
};

// 404 handler for unhandled routes
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const err = new NotFoundError(`Can't find ${req.originalUrl} on this server!`);
  next(err);
};

export default errorHandler;
