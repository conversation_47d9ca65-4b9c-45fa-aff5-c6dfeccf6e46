import { Request, Response, NextFunction } from 'express';
import { logApiRequest } from '@/utils/logger';

export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  // Store original end function
  const originalEnd = res.end;
  
  // Override end function to log when response is sent
  res.end = function(chunk?: any, encoding?: any) {
    const duration = Date.now() - startTime;
    
    logApiRequest(
      req.method,
      req.originalUrl,
      res.statusCode,
      duration,
      {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        contentLength: res.get('Content-Length'),
        userId: (req as any).user?.id,
      }
    );
    
    // Call original end function
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};
