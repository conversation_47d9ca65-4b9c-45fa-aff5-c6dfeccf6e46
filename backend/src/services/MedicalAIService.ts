import axios, { AxiosInstance } from 'axios';
import { logger } from '../utils/logger';

export interface MedicalAnalysisRequest {
  text: string;
  analysisType: 'supplement' | 'interaction' | 'side_effects' | 'dosage' | 'contraindications';
  context?: string;
}

export interface MedicalAnalysisResponse {
  analysis: string;
  entities: MedicalEntity[];
  interactions: DrugInteraction[];
  recommendations: string[];
  confidence: number;
  reasoning: string[];
}

export interface MedicalEntity {
  name: string;
  type: 'supplement' | 'ingredient' | 'condition' | 'effect';
  description: string;
  confidence: number;
}

export interface DrugInteraction {
  substance1: string;
  substance2: string;
  interactionType: 'major' | 'moderate' | 'minor';
  description: string;
  severity: number;
}

export class MedicalAIService {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.GEMMA_MEDICAL_URL || 'http://*************:1234';
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  /**
   * Analyze supplement information using Gemma3-4B-Medical with Chain-of-Thought reasoning
   */
  async analyzeSupplement(request: MedicalAnalysisRequest): Promise<MedicalAnalysisResponse> {
    try {
      const prompt = this.buildMedicalPrompt(request);
      
      const response = await this.client.post('/v1/chat/completions', {
        model: 'gemma3-4b-medical',
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt(request.analysisType)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        stream: false
      });

      const aiResponse = response.data.choices[0].message.content;
      return this.parseAIResponse(aiResponse, request.analysisType);

    } catch (error) {
      logger.error('Medical AI analysis failed:', error);
      throw new Error('Failed to analyze medical information');
    }
  }

  /**
   * Extract drug interactions from text
   */
  async analyzeInteractions(substances: string[]): Promise<DrugInteraction[]> {
    try {
      const prompt = this.buildInteractionPrompt(substances);
      
      const response = await this.client.post('/v1/chat/completions', {
        model: 'gemma3-4b-medical',
        messages: [
          {
            role: 'system',
            content: this.getInteractionSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 1500
      });

      const aiResponse = response.data.choices[0].message.content;
      return this.parseInteractions(aiResponse);

    } catch (error) {
      logger.error('Interaction analysis failed:', error);
      throw new Error('Failed to analyze drug interactions');
    }
  }

  /**
   * Build Chain-of-Thought prompt for medical analysis
   */
  private buildMedicalPrompt(request: MedicalAnalysisRequest): string {
    const basePrompt = `
Please analyze the following text about supplements/medical information using Chain-of-Thought reasoning:

TEXT TO ANALYZE:
${request.text}

ANALYSIS TYPE: ${request.analysisType}
${request.context ? `ADDITIONAL CONTEXT: ${request.context}` : ''}

Please provide your analysis following this Chain-of-Thought structure:

1. INITIAL ASSESSMENT:
   - What type of medical/supplement information is this?
   - What are the key entities mentioned?

2. DETAILED ANALYSIS:
   - Medical properties and mechanisms
   - Potential benefits and effects
   - Known side effects or risks
   - Dosage considerations

3. INTERACTION ANALYSIS:
   - Potential drug interactions
   - Contraindications
   - Special populations (pregnancy, elderly, etc.)

4. EVIDENCE EVALUATION:
   - Quality of available evidence
   - Clinical trial data if available
   - Regulatory status

5. FINAL RECOMMENDATIONS:
   - Safety profile
   - Usage recommendations
   - Monitoring requirements

Please format your response as structured JSON with clear reasoning steps.
`;

    return basePrompt;
  }

  /**
   * Get system prompt based on analysis type
   */
  private getSystemPrompt(analysisType: string): string {
    const baseSystem = `You are a medical AI assistant specialized in supplement and drug analysis. You have extensive knowledge of:
- Pharmacology and drug interactions
- Supplement science and nutrition
- Clinical research and evidence evaluation
- Safety profiles and contraindications
- Dosage recommendations and therapeutic ranges

Always use Chain-of-Thought reasoning and provide evidence-based analysis.`;

    const typeSpecific = {
      supplement: 'Focus on supplement efficacy, safety, and interactions.',
      interaction: 'Focus on drug-drug and drug-supplement interactions.',
      side_effects: 'Focus on adverse effects and safety profiles.',
      dosage: 'Focus on therapeutic dosing and safety margins.',
      contraindications: 'Focus on conditions and populations where use is not recommended.'
    };

    return `${baseSystem}\n\n${typeSpecific[analysisType as keyof typeof typeSpecific] || typeSpecific.supplement}`;
  }

  /**
   * Build interaction analysis prompt
   */
  private buildInteractionPrompt(substances: string[]): string {
    return `
Analyze potential interactions between these substances using Chain-of-Thought reasoning:

SUBSTANCES: ${substances.join(', ')}

Please provide:
1. Pairwise interaction analysis
2. Mechanism of interaction
3. Clinical significance
4. Severity rating (1-10)
5. Management recommendations

Format as structured JSON with interaction pairs.
`;
  }

  /**
   * Get interaction-specific system prompt
   */
  private getInteractionSystemPrompt(): string {
    return `You are a clinical pharmacology expert specializing in drug and supplement interactions. 
Analyze interactions based on:
- Pharmacokinetic interactions (absorption, metabolism, excretion)
- Pharmacodynamic interactions (additive, synergistic, antagonistic effects)
- Clinical evidence and case reports
- Severity and clinical significance

Always provide evidence-based analysis with confidence levels.`;
  }

  /**
   * Parse AI response into structured format
   */
  private parseAIResponse(response: string, analysisType: string): MedicalAnalysisResponse {
    try {
      // Try to parse JSON response
      const parsed = JSON.parse(response);
      return {
        analysis: parsed.analysis || response,
        entities: parsed.entities || [],
        interactions: parsed.interactions || [],
        recommendations: parsed.recommendations || [],
        confidence: parsed.confidence || 0.8,
        reasoning: parsed.reasoning || []
      };
    } catch (error) {
      // Fallback to text parsing
      return {
        analysis: response,
        entities: this.extractEntities(response),
        interactions: [],
        recommendations: this.extractRecommendations(response),
        confidence: 0.7,
        reasoning: this.extractReasoning(response)
      };
    }
  }

  /**
   * Parse interaction response
   */
  private parseInteractions(response: string): DrugInteraction[] {
    try {
      const parsed = JSON.parse(response);
      return parsed.interactions || [];
    } catch (error) {
      // Fallback parsing logic
      return [];
    }
  }

  /**
   * Extract entities from text response
   */
  private extractEntities(text: string): MedicalEntity[] {
    // Simple entity extraction - can be enhanced with NLP
    const entities: MedicalEntity[] = [];
    // Implementation would use regex or NLP to extract entities
    return entities;
  }

  /**
   * Extract recommendations from text
   */
  private extractRecommendations(text: string): string[] {
    const recommendations: string[] = [];
    // Extract recommendation sections
    return recommendations;
  }

  /**
   * Extract reasoning steps from text
   */
  private extractReasoning(text: string): string[] {
    const reasoning: string[] = [];
    // Extract numbered reasoning steps
    return reasoning;
  }

  /**
   * Health check for Gemma3-4B service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/v1/models');
      return response.status === 200;
    } catch (error) {
      logger.error('Gemma3-4B health check failed:', error);
      return false;
    }
  }
}
