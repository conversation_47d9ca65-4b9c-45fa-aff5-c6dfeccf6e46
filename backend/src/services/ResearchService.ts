import axios, { AxiosInstance } from 'axios';
import { logger } from '../utils/logger';

export interface ResearchQuery {
  query: string;
  type: 'web_search' | 'academic' | 'news' | 'supplement_specific';
  filters?: {
    timeRange?: 'day' | 'week' | 'month' | 'year';
    domains?: string[];
    excludeDomains?: string[];
    maxResults?: number;
  };
  context?: string;
}

export interface ResearchResult {
  title: string;
  url: string;
  snippet: string;
  content?: string;
  publishedDate?: string;
  domain: string;
  relevanceScore: number;
  medicalRelevance?: number;
  extractedEntities?: string[];
}

export interface ResearchResponse {
  query: string;
  results: ResearchResult[];
  totalResults: number;
  searchTime: number;
  suggestions?: string[];
  relatedQueries?: string[];
}

export class ResearchService {
  private braveClient: AxiosInstance;
  private tavilyClient: AxiosInstance;

  constructor() {
    // Brave Search API client
    this.braveClient = axios.create({
      baseURL: 'https://api.search.brave.com/res/v1',
      headers: {
        'X-Subscription-Token': process.env.BRAVE_API_KEY || '',
        'Accept': 'application/json',
      },
      timeout: 15000,
    });

    // Tavily API client
    this.tavilyClient = axios.create({
      baseURL: 'https://api.tavily.com',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 20000,
    });
  }

  /**
   * Perform web search using Brave Search API
   */
  async searchWeb(query: ResearchQuery): Promise<ResearchResponse> {
    try {
      const startTime = Date.now();
      
      const params = {
        q: this.enhanceQuery(query),
        count: query.filters?.maxResults || 10,
        offset: 0,
        mkt: 'en-US',
        safesearch: 'moderate',
        freshness: query.filters?.timeRange || undefined,
        text_decorations: false,
        spellcheck: true,
      };

      const response = await this.braveClient.get('/web/search', { params });
      const searchTime = Date.now() - startTime;

      const results = this.processBraveResults(response.data.web?.results || []);
      
      return {
        query: query.query,
        results,
        totalResults: response.data.web?.total_count || 0,
        searchTime,
        suggestions: response.data.query?.spellcheck_off ? [response.data.query.original] : [],
        relatedQueries: this.generateRelatedQueries(query.query)
      };

    } catch (error) {
      logger.error('Brave search failed:', error);
      throw new Error('Web search failed');
    }
  }

  /**
   * Perform advanced research using Tavily
   */
  async searchTavily(query: ResearchQuery): Promise<ResearchResponse> {
    try {
      const startTime = Date.now();

      const requestBody = {
        api_key: process.env.TAVILY_API_KEY,
        query: this.enhanceQuery(query),
        search_depth: query.type === 'academic' ? 'advanced' : 'basic',
        include_answer: true,
        include_raw_content: true,
        max_results: query.filters?.maxResults || 10,
        include_domains: query.filters?.domains || [],
        exclude_domains: query.filters?.excludeDomains || [],
        include_images: false,
        include_image_descriptions: false,
      };

      const response = await this.tavilyClient.post('/search', requestBody);
      const searchTime = Date.now() - startTime;

      const results = this.processTavilyResults(response.data.results || []);

      return {
        query: query.query,
        results,
        totalResults: results.length,
        searchTime,
        suggestions: [],
        relatedQueries: this.generateRelatedQueries(query.query)
      };

    } catch (error) {
      logger.error('Tavily search failed:', error);
      throw new Error('Advanced search failed');
    }
  }

  /**
   * Perform supplement-specific research
   */
  async searchSupplements(query: ResearchQuery): Promise<ResearchResponse> {
    try {
      // Enhance query for supplement research
      const supplementQuery = {
        ...query,
        query: this.enhanceSupplementQuery(query.query),
        filters: {
          ...query.filters,
          domains: [
            'pubmed.ncbi.nlm.nih.gov',
            'examine.com',
            'consumerlab.com',
            'ods.od.nih.gov',
            'nccih.nih.gov',
            'webmd.com',
            'mayoclinic.org',
            'healthline.com',
            ...(query.filters?.domains || [])
          ]
        }
      };

      // Use Tavily for more comprehensive supplement research
      return await this.searchTavily(supplementQuery);

    } catch (error) {
      logger.error('Supplement search failed:', error);
      throw new Error('Supplement research failed');
    }
  }

  /**
   * Combine multiple search sources
   */
  async comprehensiveSearch(query: ResearchQuery): Promise<ResearchResponse> {
    try {
      const [braveResults, tavilyResults] = await Promise.allSettled([
        this.searchWeb(query),
        this.searchTavily(query)
      ]);

      const combinedResults: ResearchResult[] = [];
      
      if (braveResults.status === 'fulfilled') {
        combinedResults.push(...braveResults.value.results);
      }
      
      if (tavilyResults.status === 'fulfilled') {
        combinedResults.push(...tavilyResults.value.results);
      }

      // Remove duplicates and sort by relevance
      const uniqueResults = this.deduplicateResults(combinedResults);
      const sortedResults = uniqueResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

      return {
        query: query.query,
        results: sortedResults.slice(0, query.filters?.maxResults || 20),
        totalResults: sortedResults.length,
        searchTime: 0,
        suggestions: [],
        relatedQueries: this.generateRelatedQueries(query.query)
      };

    } catch (error) {
      logger.error('Comprehensive search failed:', error);
      throw new Error('Research failed');
    }
  }

  /**
   * Enhance query for better medical/supplement results
   */
  private enhanceQuery(query: ResearchQuery): string {
    let enhanced = query.query;

    if (query.type === 'supplement_specific') {
      enhanced += ' supplement benefits side effects dosage clinical studies';
    } else if (query.type === 'academic') {
      enhanced += ' research study clinical trial pubmed';
    } else if (query.type === 'news') {
      enhanced += ' latest news recent developments';
    }

    return enhanced;
  }

  /**
   * Enhance query specifically for supplement research
   */
  private enhanceSupplementQuery(query: string): string {
    const supplementKeywords = [
      'supplement',
      'benefits',
      'side effects',
      'dosage',
      'clinical studies',
      'safety',
      'interactions',
      'efficacy',
      'research'
    ];

    return `${query} ${supplementKeywords.join(' ')}`;
  }

  /**
   * Process Brave search results
   */
  private processBraveResults(results: any[]): ResearchResult[] {
    return results.map(result => ({
      title: result.title || '',
      url: result.url || '',
      snippet: result.description || '',
      publishedDate: result.age || undefined,
      domain: this.extractDomain(result.url || ''),
      relevanceScore: this.calculateRelevance(result),
      medicalRelevance: this.calculateMedicalRelevance(result)
    }));
  }

  /**
   * Process Tavily search results
   */
  private processTavilyResults(results: any[]): ResearchResult[] {
    return results.map(result => ({
      title: result.title || '',
      url: result.url || '',
      snippet: result.content || '',
      content: result.raw_content || undefined,
      publishedDate: result.published_date || undefined,
      domain: this.extractDomain(result.url || ''),
      relevanceScore: result.score || 0.5,
      medicalRelevance: this.calculateMedicalRelevance(result)
    }));
  }

  /**
   * Extract domain from URL
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * Calculate relevance score
   */
  private calculateRelevance(result: any): number {
    let score = 0.5;
    
    // Boost score for medical domains
    const medicalDomains = ['pubmed', 'nih.gov', 'mayoclinic', 'webmd', 'examine.com'];
    if (medicalDomains.some(domain => result.url?.includes(domain))) {
      score += 0.3;
    }

    // Boost for recent content
    if (result.age && result.age.includes('day')) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate medical relevance score
   */
  private calculateMedicalRelevance(result: any): number {
    const medicalKeywords = [
      'clinical', 'study', 'research', 'trial', 'supplement', 'vitamin',
      'mineral', 'dosage', 'side effects', 'benefits', 'safety'
    ];

    const text = `${result.title} ${result.description || result.content || ''}`.toLowerCase();
    const matches = medicalKeywords.filter(keyword => text.includes(keyword));
    
    return matches.length / medicalKeywords.length;
  }

  /**
   * Remove duplicate results
   */
  private deduplicateResults(results: ResearchResult[]): ResearchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      const key = result.url;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Generate related queries
   */
  private generateRelatedQueries(query: string): string[] {
    const baseQuery = query.toLowerCase();
    const related = [
      `${baseQuery} benefits`,
      `${baseQuery} side effects`,
      `${baseQuery} dosage`,
      `${baseQuery} interactions`,
      `${baseQuery} clinical studies`
    ];

    return related.filter(q => q !== baseQuery);
  }

  /**
   * Health check for research services
   */
  async healthCheck(): Promise<{ brave: boolean; tavily: boolean }> {
    const checks = await Promise.allSettled([
      this.braveClient.get('/web/search?q=test&count=1'),
      this.tavilyClient.post('/search', {
        api_key: process.env.TAVILY_API_KEY,
        query: 'test',
        max_results: 1
      })
    ]);

    return {
      brave: checks[0].status === 'fulfilled',
      tavily: checks[1].status === 'fulfilled'
    };
  }
}
