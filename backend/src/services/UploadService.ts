import fs from 'fs/promises';
import path from 'path';
import { mongoose } from '@/config/mongodb';
import { RAGService } from '@/services/RAGService';
import { AIService } from '@/services/AIService';
import { config } from '@/config/environment';
import { logger, logDatabaseOperation, logError } from '@/utils/logger';
import { DatabaseError, NotFoundError, ValidationError } from '@/middleware/errorHandler';
import { v4 as uuidv4 } from 'uuid';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';
import axios from 'axios';

interface ProcessOptions {
  extractText?: boolean;
  addToKnowledgeBase?: boolean;
  source?: string;
  metadata?: any;
}

interface ListOptions {
  page?: number;
  limit?: number;
  type?: string;
  source?: string;
  search?: string;
}

interface BatchOptions {
  [key: string]: any;
}

// MongoDB schema for uploaded files
const fileSchema = new mongoose.Schema({
  id: { type: String, unique: true, required: true },
  originalName: { type: String, required: true },
  fileName: { type: String, required: true },
  filePath: { type: String, required: true },
  mimeType: { type: String, required: true },
  size: { type: Number, required: true },
  extractedText: { type: String },
  metadata: { type: mongoose.Schema.Types.Mixed },
  source: { type: String },
  processed: { type: Boolean, default: false },
  addedToKnowledgeBase: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const FileModel = mongoose.model('UploadedFile', fileSchema);

export class UploadService {
  private ragService: RAGService;
  private aiService: AIService;

  constructor() {
    this.ragService = new RAGService();
    this.aiService = new AIService();
  }

  async processFile(file: Express.Multer.File, options: ProcessOptions): Promise<any> {
    const startTime = Date.now();

    try {
      const fileId = uuidv4();
      let extractedText = '';

      // Extract text if requested
      if (options.extractText) {
        extractedText = await this.extractTextFromFile(file);
      }

      // Save file metadata to database
      const fileDoc = new FileModel({
        id: fileId,
        originalName: file.originalname,
        fileName: file.filename,
        filePath: file.path,
        mimeType: file.mimetype,
        size: file.size,
        extractedText,
        metadata: options.metadata || {},
        source: options.source,
        processed: true,
        addedToKnowledgeBase: false,
      });

      await fileDoc.save();

      // Add to knowledge base if requested
      if (options.addToKnowledgeBase && extractedText) {
        await this.addToKnowledgeBase(fileId, extractedText, {
          title: file.originalname,
          source: options.source || 'file_upload',
          fileName: file.filename,
          mimeType: file.mimetype,
          ...options.metadata,
        });

        fileDoc.addedToKnowledgeBase = true;
        await fileDoc.save();
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Process file', 'Upload', duration, {
        fileName: file.originalname,
        size: file.size,
        extractedText: !!extractedText,
        addedToKnowledgeBase: options.addToKnowledgeBase && !!extractedText,
      });

      return {
        id: fileId,
        originalName: file.originalname,
        fileName: file.filename,
        mimeType: file.mimetype,
        size: file.size,
        extractedText: extractedText ? extractedText.substring(0, 500) + '...' : null,
        addedToKnowledgeBase: fileDoc.addedToKnowledgeBase,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to process file', error, { fileName: file.originalname, options });
      throw new DatabaseError('File processing failed');
    }
  }

  async processFiles(files: Express.Multer.File[], options: ProcessOptions): Promise<any> {
    const startTime = Date.now();

    try {
      const results = [];
      const errors = [];

      for (const file of files) {
        try {
          const result = await this.processFile(file, options);
          results.push(result);
        } catch (error) {
          errors.push({
            fileName: file.originalname,
            error: error.message,
          });
        }
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Process files', 'Upload', duration, {
        totalFiles: files.length,
        successful: results.length,
        errors: errors.length,
      });

      return {
        successful: results,
        errors,
        totalFiles: files.length,
        successCount: results.length,
        errorCount: errors.length,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to process files', error, { fileCount: files.length, options });
      throw new DatabaseError('Batch file processing failed');
    }
  }

  async processUrl(url: string, options: ProcessOptions): Promise<any> {
    const startTime = Date.now();

    try {
      // Download content from URL
      const response = await axios.get(url, {
        timeout: 30000,
        maxContentLength: config.upload.maxFileSize,
      });

      const content = response.data;
      const contentType = response.headers['content-type'] || 'text/plain';
      
      let extractedText = '';
      
      if (options.extractText) {
        if (contentType.includes('text/') || contentType.includes('application/json')) {
          extractedText = typeof content === 'string' ? content : JSON.stringify(content);
        } else {
          throw new ValidationError('Cannot extract text from this content type');
        }
      }

      const fileId = uuidv4();
      const fileName = path.basename(new URL(url).pathname) || 'downloaded_content';

      // Save to database
      const fileDoc = new FileModel({
        id: fileId,
        originalName: fileName,
        fileName: fileName,
        filePath: url, // Store URL as path for URL-based files
        mimeType: contentType,
        size: Buffer.byteLength(extractedText || content),
        extractedText,
        metadata: { url, ...options.metadata },
        source: options.source || 'url_download',
        processed: true,
        addedToKnowledgeBase: false,
      });

      await fileDoc.save();

      // Add to knowledge base if requested
      if (options.addToKnowledgeBase && extractedText) {
        await this.addToKnowledgeBase(fileId, extractedText, {
          title: fileName,
          source: options.source || 'url_download',
          url,
          ...options.metadata,
        });

        fileDoc.addedToKnowledgeBase = true;
        await fileDoc.save();
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Process URL', 'Upload', duration, {
        url,
        contentType,
        size: fileDoc.size,
        extractedText: !!extractedText,
      });

      return {
        id: fileId,
        originalName: fileName,
        url,
        mimeType: contentType,
        size: fileDoc.size,
        extractedText: extractedText ? extractedText.substring(0, 500) + '...' : null,
        addedToKnowledgeBase: fileDoc.addedToKnowledgeBase,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to process URL', error, { url, options });
      throw new DatabaseError('URL processing failed');
    }
  }

  async getFile(id: string): Promise<any> {
    const startTime = Date.now();

    try {
      const file = await FileModel.findOne({ id });
      
      if (!file) {
        throw new NotFoundError(`File with id ${id} not found`);
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Get file', 'Upload', duration, { id });

      return {
        id: file.id,
        originalName: file.originalName,
        fileName: file.fileName,
        filePath: file.filePath,
        mimeType: file.mimeType,
        size: file.size,
        metadata: file.metadata,
        source: file.source,
        processed: file.processed,
        addedToKnowledgeBase: file.addedToKnowledgeBase,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to get file', error, { id });
      throw new DatabaseError('File retrieval failed');
    }
  }

  async getFileContent(id: string): Promise<any> {
    const startTime = Date.now();

    try {
      const file = await FileModel.findOne({ id });
      
      if (!file) {
        throw new NotFoundError(`File with id ${id} not found`);
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Get file content', 'Upload', duration, { id });

      return {
        id: file.id,
        originalName: file.originalName,
        extractedText: file.extractedText,
        mimeType: file.mimeType,
        size: file.size,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to get file content', error, { id });
      throw new DatabaseError('File content retrieval failed');
    }
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async listFiles(options: ListOptions): Promise<any> {
    const startTime = Date.now();

    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 20, 100);
      const skip = (page - 1) * limit;

      // Build query
      const query: any = {};
      
      if (options.type) {
        query.mimeType = { $regex: options.type, $options: 'i' };
      }
      
      if (options.source) {
        query.source = options.source;
      }
      
      if (options.search) {
        query.$or = [
          { originalName: { $regex: options.search, $options: 'i' } },
          { extractedText: { $regex: options.search, $options: 'i' } },
        ];
      }

      // Execute query
      const [files, totalCount] = await Promise.all([
        FileModel.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        FileModel.countDocuments(query),
      ]);

      const duration = Date.now() - startTime;
      logDatabaseOperation('List files', 'Upload', duration, {
        resultCount: files.length,
        totalCount,
        page,
        limit,
      });

      return {
        files: files.map(file => ({
          id: file.id,
          originalName: file.originalName,
          fileName: file.fileName,
          mimeType: file.mimeType,
          size: file.size,
          source: file.source,
          processed: file.processed,
          addedToKnowledgeBase: file.addedToKnowledgeBase,
          createdAt: file.createdAt,
          updatedAt: file.updatedAt,
        })),
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: page * limit < totalCount,
          hasPrev: page > 1,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to list files', error, { options });
      throw new DatabaseError('File listing failed');
    }
  }

  async deleteFile(id: string): Promise<void> {
    const startTime = Date.now();

    try {
      const file = await FileModel.findOne({ id });

      if (!file) {
        throw new NotFoundError(`File with id ${id} not found`);
      }

      // Delete physical file if it exists and is not a URL
      if (!file.filePath.startsWith('http') && await this.fileExists(file.filePath)) {
        await fs.unlink(file.filePath);
      }

      // Delete from database
      await FileModel.deleteOne({ id });

      const duration = Date.now() - startTime;
      logDatabaseOperation('Delete file', 'Upload', duration, { id });
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to delete file', error, { id });
      throw new DatabaseError('File deletion failed');
    }
  }

  async batchProcess(fileIds: string[], operation: string, options: BatchOptions): Promise<any> {
    const startTime = Date.now();

    try {
      const results = [];
      const errors = [];

      for (const fileId of fileIds) {
        try {
          let result;

          switch (operation) {
            case 'addToKnowledgeBase':
              result = await this.addFileToKnowledgeBase(fileId, options);
              break;
            case 'extractText':
              result = await this.extractTextFromFileId(fileId);
              break;
            case 'delete':
              await this.deleteFile(fileId);
              result = { id: fileId, deleted: true };
              break;
            default:
              throw new ValidationError(`Unknown operation: ${operation}`);
          }

          results.push(result);
        } catch (error) {
          errors.push({
            fileId,
            error: error.message,
          });
        }
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Batch process', 'Upload', duration, {
        operation,
        totalFiles: fileIds.length,
        successful: results.length,
        errors: errors.length,
      });

      return {
        operation,
        successful: results,
        errors,
        totalFiles: fileIds.length,
        successCount: results.length,
        errorCount: errors.length,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to batch process files', error, { fileIds, operation, options });
      throw new DatabaseError('Batch processing failed');
    }
  }

  async getStats(): Promise<any> {
    const startTime = Date.now();

    try {
      const [
        totalFiles,
        processedFiles,
        knowledgeBaseFiles,
        sizeStats,
        typeStats,
      ] = await Promise.all([
        FileModel.countDocuments(),
        FileModel.countDocuments({ processed: true }),
        FileModel.countDocuments({ addedToKnowledgeBase: true }),
        FileModel.aggregate([
          {
            $group: {
              _id: null,
              totalSize: { $sum: '$size' },
              avgSize: { $avg: '$size' },
              maxSize: { $max: '$size' },
              minSize: { $min: '$size' },
            },
          },
        ]),
        FileModel.aggregate([
          {
            $group: {
              _id: '$mimeType',
              count: { $sum: 1 },
              totalSize: { $sum: '$size' },
            },
          },
          { $sort: { count: -1 } },
        ]),
      ]);

      const duration = Date.now() - startTime;
      logDatabaseOperation('Get upload stats', 'Upload', duration);

      return {
        totalFiles,
        processedFiles,
        knowledgeBaseFiles,
        processingRate: totalFiles > 0 ? (processedFiles / totalFiles) * 100 : 0,
        knowledgeBaseRate: totalFiles > 0 ? (knowledgeBaseFiles / totalFiles) * 100 : 0,
        sizeStats: sizeStats[0] || {
          totalSize: 0,
          avgSize: 0,
          maxSize: 0,
          minSize: 0,
        },
        typeStats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to get upload stats', error);
      throw new DatabaseError('Failed to get upload statistics');
    }
  }

  private async extractTextFromFile(file: Express.Multer.File): Promise<string> {
    try {
      const buffer = await fs.readFile(file.path);

      switch (file.mimetype) {
        case 'application/pdf':
          const pdfData = await pdfParse(buffer);
          return pdfData.text;

        case 'application/msword':
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          const docData = await mammoth.extractRawText({ buffer });
          return docData.value;

        case 'text/plain':
        case 'text/csv':
        case 'application/json':
        case 'application/xml':
        case 'text/xml':
          return buffer.toString('utf-8');

        default:
          throw new ValidationError(`Text extraction not supported for ${file.mimetype}`);
      }
    } catch (error) {
      logError('Failed to extract text from file', error, {
        fileName: file.originalname,
        mimeType: file.mimetype,
      });
      throw new DatabaseError('Text extraction failed');
    }
  }

  private async extractTextFromFileId(fileId: string): Promise<any> {
    const file = await FileModel.findOne({ id: fileId });

    if (!file) {
      throw new NotFoundError(`File with id ${fileId} not found`);
    }

    if (file.extractedText) {
      return {
        id: fileId,
        extractedText: file.extractedText,
        alreadyExtracted: true,
      };
    }

    // For URL-based files, we can't re-extract
    if (file.filePath.startsWith('http')) {
      throw new ValidationError('Cannot re-extract text from URL-based files');
    }

    // Check if physical file exists
    if (!await this.fileExists(file.filePath)) {
      throw new NotFoundError('Physical file not found');
    }

    // Create a mock file object for extraction
    const mockFile = {
      path: file.filePath,
      originalname: file.originalName,
      mimetype: file.mimeType,
    } as Express.Multer.File;

    const extractedText = await this.extractTextFromFile(mockFile);

    // Update database
    file.extractedText = extractedText;
    file.updatedAt = new Date();
    await file.save();

    return {
      id: fileId,
      extractedText,
      alreadyExtracted: false,
    };
  }

  private async addToKnowledgeBase(fileId: string, text: string, metadata: any): Promise<void> {
    try {
      await this.ragService.addDocument({
        content: text,
        title: metadata.title,
        source: metadata.source,
        metadata: {
          fileId,
          ...metadata,
        },
        className: 'Document',
      });
    } catch (error) {
      logError('Failed to add file to knowledge base', error, { fileId, metadata });
      throw new DatabaseError('Knowledge base addition failed');
    }
  }

  private async addFileToKnowledgeBase(fileId: string, options: BatchOptions): Promise<any> {
    const file = await FileModel.findOne({ id: fileId });

    if (!file) {
      throw new NotFoundError(`File with id ${fileId} not found`);
    }

    if (file.addedToKnowledgeBase) {
      return {
        id: fileId,
        addedToKnowledgeBase: true,
        alreadyAdded: true,
      };
    }

    if (!file.extractedText) {
      throw new ValidationError('File has no extracted text to add to knowledge base');
    }

    await this.addToKnowledgeBase(fileId, file.extractedText, {
      title: file.originalName,
      source: file.source || 'file_upload',
      fileName: file.fileName,
      mimeType: file.mimeType,
      ...file.metadata,
      ...options,
    });

    // Update database
    file.addedToKnowledgeBase = true;
    file.updatedAt = new Date();
    await file.save();

    return {
      id: fileId,
      addedToKnowledgeBase: true,
      alreadyAdded: false,
    };
  }
}
