import { spawn, ChildProcess } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { logger } from '../utils/logger';

export interface CrawlRequest {
  url: string;
  type: 'single_page' | 'recursive' | 'sitemap' | 'llms_txt';
  options?: {
    maxDepth?: number;
    maxPages?: number;
    includePatterns?: string[];
    excludePatterns?: string[];
    extractImages?: boolean;
    chunkSize?: number;
    followExternalLinks?: boolean;
  };
}

export interface CrawlResult {
  url: string;
  title: string;
  content: string;
  chunks: ContentChunk[];
  metadata: PageMetadata;
  extractedData?: ExtractedData;
  crawlTime: number;
}

export interface ContentChunk {
  id: string;
  content: string;
  headers: string[];
  wordCount: number;
  charCount: number;
  chunkIndex: number;
}

export interface PageMetadata {
  title: string;
  description?: string;
  keywords?: string[];
  author?: string;
  publishedDate?: string;
  lastModified?: string;
  language?: string;
  domain: string;
}

export interface ExtractedData {
  supplements: string[];
  ingredients: string[];
  dosages: string[];
  benefits: string[];
  sideEffects: string[];
  interactions: string[];
}

export interface CrawlProgress {
  totalPages: number;
  completedPages: number;
  currentUrl: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  errors: string[];
}

export class CrawlService {
  private crawlScriptPath: string;
  private tempDir: string;
  private activeCrawls: Map<string, ChildProcess> = new Map();

  constructor() {
    this.crawlScriptPath = path.join(__dirname, '../../scripts/crawl4ai_wrapper.py');
    this.tempDir = path.join(__dirname, '../../temp/crawl');
    this.ensureTempDir();
  }

  /**
   * Crawl a single page
   */
  async crawlSinglePage(request: CrawlRequest): Promise<CrawlResult> {
    try {
      const startTime = Date.now();
      const crawlId = this.generateCrawlId();
      
      const result = await this.executeCrawl(crawlId, {
        ...request,
        type: 'single_page'
      });

      const crawlTime = Date.now() - startTime;
      
      return {
        ...result,
        crawlTime
      };

    } catch (error) {
      logger.error('Single page crawl failed:', error);
      throw new Error('Failed to crawl page');
    }
  }

  /**
   * Crawl website recursively
   */
  async crawlRecursive(request: CrawlRequest): Promise<CrawlResult[]> {
    try {
      const crawlId = this.generateCrawlId();
      
      const results = await this.executeBatchCrawl(crawlId, {
        ...request,
        type: 'recursive'
      });

      return results;

    } catch (error) {
      logger.error('Recursive crawl failed:', error);
      throw new Error('Failed to crawl website recursively');
    }
  }

  /**
   * Crawl from sitemap
   */
  async crawlSitemap(request: CrawlRequest): Promise<CrawlResult[]> {
    try {
      const crawlId = this.generateCrawlId();
      
      const results = await this.executeBatchCrawl(crawlId, {
        ...request,
        type: 'sitemap'
      });

      return results;

    } catch (error) {
      logger.error('Sitemap crawl failed:', error);
      throw new Error('Failed to crawl sitemap');
    }
  }

  /**
   * Crawl LLMs.txt or markdown file
   */
  async crawlMarkdown(request: CrawlRequest): Promise<CrawlResult> {
    try {
      const startTime = Date.now();
      const crawlId = this.generateCrawlId();
      
      const result = await this.executeCrawl(crawlId, {
        ...request,
        type: 'llms_txt'
      });

      const crawlTime = Date.now() - startTime;
      
      return {
        ...result,
        crawlTime
      };

    } catch (error) {
      logger.error('Markdown crawl failed:', error);
      throw new Error('Failed to crawl markdown content');
    }
  }

  /**
   * Get crawl progress
   */
  async getCrawlProgress(crawlId: string): Promise<CrawlProgress> {
    try {
      const progressFile = path.join(this.tempDir, `${crawlId}_progress.json`);
      const progressData = await fs.readFile(progressFile, 'utf-8');
      return JSON.parse(progressData);
    } catch (error) {
      return {
        totalPages: 0,
        completedPages: 0,
        currentUrl: '',
        status: 'failed',
        errors: ['Progress file not found']
      };
    }
  }

  /**
   * Cancel active crawl
   */
  async cancelCrawl(crawlId: string): Promise<boolean> {
    try {
      const process = this.activeCrawls.get(crawlId);
      if (process) {
        process.kill('SIGTERM');
        this.activeCrawls.delete(crawlId);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Failed to cancel crawl:', error);
      return false;
    }
  }

  /**
   * Execute single crawl
   */
  private async executeCrawl(crawlId: string, request: CrawlRequest): Promise<CrawlResult> {
    return new Promise((resolve, reject) => {
      const outputFile = path.join(this.tempDir, `${crawlId}_result.json`);
      
      const args = [
        this.crawlScriptPath,
        '--url', request.url,
        '--type', request.type,
        '--output', outputFile,
        '--crawl-id', crawlId
      ];

      // Add optional parameters
      if (request.options?.maxDepth) {
        args.push('--max-depth', request.options.maxDepth.toString());
      }
      if (request.options?.chunkSize) {
        args.push('--chunk-size', request.options.chunkSize.toString());
      }
      if (request.options?.extractImages) {
        args.push('--extract-images');
      }

      const crawlProcess = spawn('python3', args);
      this.activeCrawls.set(crawlId, crawlProcess);

      let stdout = '';
      let stderr = '';

      crawlProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      crawlProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      crawlProcess.on('close', async (code) => {
        this.activeCrawls.delete(crawlId);
        
        if (code === 0) {
          try {
            const resultData = await fs.readFile(outputFile, 'utf-8');
            const result = JSON.parse(resultData);
            
            // Clean up temp files
            await this.cleanupTempFiles(crawlId);
            
            resolve(this.processCrawlResult(result));
          } catch (error) {
            reject(new Error('Failed to parse crawl result'));
          }
        } else {
          reject(new Error(`Crawl failed with code ${code}: ${stderr}`));
        }
      });

      crawlProcess.on('error', (error) => {
        this.activeCrawls.delete(crawlId);
        reject(error);
      });
    });
  }

  /**
   * Execute batch crawl for multiple pages
   */
  private async executeBatchCrawl(crawlId: string, request: CrawlRequest): Promise<CrawlResult[]> {
    return new Promise((resolve, reject) => {
      const outputFile = path.join(this.tempDir, `${crawlId}_batch_result.json`);
      
      const args = [
        this.crawlScriptPath,
        '--url', request.url,
        '--type', request.type,
        '--output', outputFile,
        '--crawl-id', crawlId,
        '--batch-mode'
      ];

      // Add batch-specific options
      if (request.options?.maxPages) {
        args.push('--max-pages', request.options.maxPages.toString());
      }
      if (request.options?.maxDepth) {
        args.push('--max-depth', request.options.maxDepth.toString());
      }

      const crawlProcess = spawn('python3', args);
      this.activeCrawls.set(crawlId, crawlProcess);

      let stdout = '';
      let stderr = '';

      crawlProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      crawlProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      crawlProcess.on('close', async (code) => {
        this.activeCrawls.delete(crawlId);
        
        if (code === 0) {
          try {
            const resultData = await fs.readFile(outputFile, 'utf-8');
            const results = JSON.parse(resultData);
            
            // Clean up temp files
            await this.cleanupTempFiles(crawlId);
            
            resolve(results.map((result: any) => this.processCrawlResult(result)));
          } catch (error) {
            reject(new Error('Failed to parse batch crawl results'));
          }
        } else {
          reject(new Error(`Batch crawl failed with code ${code}: ${stderr}`));
        }
      });

      crawlProcess.on('error', (error) => {
        this.activeCrawls.delete(crawlId);
        reject(error);
      });
    });
  }

  /**
   * Process crawl result and extract supplement data
   */
  private processCrawlResult(rawResult: any): CrawlResult {
    const extractedData = this.extractSupplementData(rawResult.content || '');
    
    return {
      url: rawResult.url || '',
      title: rawResult.title || '',
      content: rawResult.content || '',
      chunks: rawResult.chunks || [],
      metadata: {
        title: rawResult.title || '',
        description: rawResult.description,
        keywords: rawResult.keywords,
        author: rawResult.author,
        publishedDate: rawResult.publishedDate,
        lastModified: rawResult.lastModified,
        language: rawResult.language,
        domain: this.extractDomain(rawResult.url || '')
      },
      extractedData,
      crawlTime: 0 // Will be set by calling function
    };
  }

  /**
   * Extract supplement-specific data from content
   */
  private extractSupplementData(content: string): ExtractedData {
    const text = content.toLowerCase();
    
    // Simple keyword extraction - can be enhanced with NLP
    const supplementKeywords = ['vitamin', 'mineral', 'supplement', 'extract', 'powder', 'capsule'];
    const dosageKeywords = ['mg', 'mcg', 'iu', 'gram', 'dose', 'daily'];
    const benefitKeywords = ['benefit', 'improve', 'enhance', 'support', 'boost'];
    const sideEffectKeywords = ['side effect', 'adverse', 'reaction', 'warning', 'caution'];
    
    return {
      supplements: this.extractKeywords(text, supplementKeywords),
      ingredients: [],
      dosages: this.extractKeywords(text, dosageKeywords),
      benefits: this.extractKeywords(text, benefitKeywords),
      sideEffects: this.extractKeywords(text, sideEffectKeywords),
      interactions: []
    };
  }

  /**
   * Extract keywords from text
   */
  private extractKeywords(text: string, keywords: string[]): string[] {
    const found: string[] = [];
    keywords.forEach(keyword => {
      if (text.includes(keyword)) {
        found.push(keyword);
      }
    });
    return found;
  }

  /**
   * Extract domain from URL
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * Generate unique crawl ID
   */
  private generateCrawlId(): string {
    return `crawl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Ensure temp directory exists
   */
  private async ensureTempDir(): Promise<void> {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      logger.error('Failed to create temp directory:', error);
    }
  }

  /**
   * Clean up temporary files
   */
  private async cleanupTempFiles(crawlId: string): Promise<void> {
    try {
      const files = await fs.readdir(this.tempDir);
      const crawlFiles = files.filter(file => file.startsWith(crawlId));
      
      await Promise.all(
        crawlFiles.map(file => 
          fs.unlink(path.join(this.tempDir, file)).catch(() => {})
        )
      );
    } catch (error) {
      logger.error('Failed to cleanup temp files:', error);
    }
  }

  /**
   * Health check for crawl service
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Check if Python and required packages are available
      return new Promise((resolve) => {
        const testProcess = spawn('python3', ['-c', 'import crawl4ai; print("OK")']);
        testProcess.on('close', (code) => {
          resolve(code === 0);
        });
        testProcess.on('error', () => {
          resolve(false);
        });
      });
    } catch (error) {
      return false;
    }
  }
}
