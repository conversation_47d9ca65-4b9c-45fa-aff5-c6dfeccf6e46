import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { config } from '@/config/environment';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}${info.stack ? '\n' + info.stack : ''}`
  )
);

// Define which transports the logger must use
const transports: winston.transport[] = [
  // Console transport
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    ),
  }),
];

// Add file transports only in non-test environment
if (config.nodeEnv !== 'test') {
  // Daily rotate file for all logs
  transports.push(
    new DailyRotateFile({
      filename: 'logs/application-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
      format: winston.format.combine(
        winston.format.uncolorize(),
        winston.format.json()
      ),
    })
  );

  // Daily rotate file for error logs only
  transports.push(
    new DailyRotateFile({
      level: 'error',
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: winston.format.combine(
        winston.format.uncolorize(),
        winston.format.json()
      ),
    })
  );
}

// Create the logger
export const logger = winston.createLogger({
  level: config.logging.level,
  levels,
  format,
  transports,
  // Don't exit on handled exceptions
  exitOnError: false,
});

// Create a stream object with a 'write' function that will be used by Morgan
export const loggerStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Helper functions for structured logging
export const logInfo = (message: string, meta?: any) => {
  logger.info(message, meta);
};

export const logError = (message: string, error?: Error | any, meta?: any) => {
  if (error instanceof Error) {
    logger.error(message, { error: error.message, stack: error.stack, ...meta });
  } else {
    logger.error(message, { error, ...meta });
  }
};

export const logWarn = (message: string, meta?: any) => {
  logger.warn(message, meta);
};

export const logDebug = (message: string, meta?: any) => {
  logger.debug(message, meta);
};

// Performance logging helper
export const logPerformance = (operation: string, startTime: number, meta?: any) => {
  const duration = Date.now() - startTime;
  logger.info(`Performance: ${operation} completed in ${duration}ms`, { duration, operation, ...meta });
};

// Database operation logging
export const logDatabaseOperation = (operation: string, collection: string, duration?: number, meta?: any) => {
  const message = `Database: ${operation} on ${collection}${duration ? ` (${duration}ms)` : ''}`;
  logger.debug(message, { operation, collection, duration, ...meta });
};

// API request logging
export const logApiRequest = (method: string, url: string, statusCode: number, duration: number, meta?: any) => {
  const level = statusCode >= 400 ? 'warn' : 'info';
  const message = `API: ${method} ${url} - ${statusCode} (${duration}ms)`;
  logger.log(level, message, { method, url, statusCode, duration, ...meta });
};

// AI operation logging
export const logAiOperation = (operation: string, model: string, tokens?: number, duration?: number, meta?: any) => {
  const message = `AI: ${operation} with ${model}${tokens ? ` (${tokens} tokens)` : ''}${duration ? ` (${duration}ms)` : ''}`;
  logger.info(message, { operation, model, tokens, duration, ...meta });
};

// Graph operation logging
export const logGraphOperation = (operation: string, nodeCount?: number, relationshipCount?: number, duration?: number, meta?: any) => {
  const message = `Graph: ${operation}${nodeCount ? ` (${nodeCount} nodes)` : ''}${relationshipCount ? ` (${relationshipCount} relationships)` : ''}${duration ? ` (${duration}ms)` : ''}`;
  logger.info(message, { operation, nodeCount, relationshipCount, duration, ...meta });
};

// Security logging
export const logSecurityEvent = (event: string, severity: 'low' | 'medium' | 'high' | 'critical', meta?: any) => {
  const level = severity === 'critical' || severity === 'high' ? 'error' : 'warn';
  const message = `Security: ${event} (${severity})`;
  logger.log(level, message, { event, severity, ...meta });
};

// Business logic logging
export const logBusinessEvent = (event: string, entityType?: string, entityId?: string, meta?: any) => {
  const message = `Business: ${event}${entityType ? ` for ${entityType}` : ''}${entityId ? ` (${entityId})` : ''}`;
  logger.info(message, { event, entityType, entityId, ...meta });
};

// Export default logger
export default logger;
