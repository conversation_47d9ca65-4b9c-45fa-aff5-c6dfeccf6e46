import { Router, Request, Response } from 'express';
import { catchAsync, ValidationError } from '@/middleware/errorHandler';
import { body, query, validationResult } from 'express-validator';
import { AIService } from '@/services/AIService';

const router = Router();
const aiService = new AIService();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError(`Validation failed: ${errors.array().map(e => e.msg).join(', ')}`);
  }
  next();
};

// Analyze supplement information
router.post('/analyze', [
  body('text').notEmpty().withMessage('Text is required'),
  body('analysisType').optional().isIn(['supplement', 'ingredient', 'interaction', 'study', 'general']).withMessage('Invalid analysis type'),
  body('model').optional().isString().withMessage('Model must be a string'),
  body('includeConfidence').optional().isBoolean().withMessage('includeConfidence must be a boolean'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    text,
    analysisType = 'general',
    model,
    includeConfidence = true,
  } = req.body;

  const options = {
    analysisType,
    model,
    includeConfidence,
  };

  const analysis = await aiService.analyzeText(text, options);

  res.status(200).json({
    success: true,
    data: analysis,
    timestamp: new Date().toISOString(),
  });
}));

// Extract entities and relationships
router.post('/extract', [
  body('text').notEmpty().withMessage('Text is required'),
  body('entityTypes').optional().isArray().withMessage('entityTypes must be an array'),
  body('relationshipTypes').optional().isArray().withMessage('relationshipTypes must be an array'),
  body('model').optional().isString().withMessage('Model must be a string'),
  body('confidence').optional().isFloat({ min: 0, max: 1 }).withMessage('confidence must be between 0 and 1'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    text,
    entityTypes,
    relationshipTypes,
    model,
    confidence = 0.7,
  } = req.body;

  const options = {
    entityTypes,
    relationshipTypes,
    model,
    confidence,
  };

  const extraction = await aiService.extractEntitiesAndRelationships(text, options);

  res.status(200).json({
    success: true,
    data: extraction,
    timestamp: new Date().toISOString(),
  });
}));

// Generate supplement recommendations
router.post('/recommend', [
  body('conditions').optional().isArray().withMessage('conditions must be an array'),
  body('goals').optional().isArray().withMessage('goals must be an array'),
  body('currentSupplements').optional().isArray().withMessage('currentSupplements must be an array'),
  body('restrictions').optional().isArray().withMessage('restrictions must be an array'),
  body('maxRecommendations').optional().isInt({ min: 1, max: 20 }).withMessage('maxRecommendations must be between 1 and 20'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    conditions = [],
    goals = [],
    currentSupplements = [],
    restrictions = [],
    maxRecommendations = 5,
  } = req.body;

  const options = {
    conditions,
    goals,
    currentSupplements,
    restrictions,
    maxRecommendations,
  };

  const recommendations = await aiService.generateRecommendations(options);

  res.status(200).json({
    success: true,
    data: recommendations,
    timestamp: new Date().toISOString(),
  });
}));

// Check for interactions
router.post('/interactions', [
  body('supplements').isArray().withMessage('supplements must be an array'),
  body('supplements').isLength({ min: 2 }).withMessage('At least 2 supplements are required'),
  body('includeFood').optional().isBoolean().withMessage('includeFood must be a boolean'),
  body('includeMedications').optional().isBoolean().withMessage('includeMedications must be a boolean'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    supplements,
    includeFood = false,
    includeMedications = true,
  } = req.body;

  const options = {
    includeFood,
    includeMedications,
  };

  const interactions = await aiService.checkInteractions(supplements, options);

  res.status(200).json({
    success: true,
    data: interactions,
    timestamp: new Date().toISOString(),
  });
}));

// Summarize research
router.post('/summarize', [
  body('text').notEmpty().withMessage('Text is required'),
  body('summaryType').optional().isIn(['brief', 'detailed', 'technical', 'consumer']).withMessage('Invalid summary type'),
  body('maxLength').optional().isInt({ min: 50, max: 2000 }).withMessage('maxLength must be between 50 and 2000'),
  body('focusAreas').optional().isArray().withMessage('focusAreas must be an array'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    text,
    summaryType = 'brief',
    maxLength = 500,
    focusAreas = [],
  } = req.body;

  const options = {
    summaryType,
    maxLength,
    focusAreas,
  };

  const summary = await aiService.summarizeText(text, options);

  res.status(200).json({
    success: true,
    data: summary,
    timestamp: new Date().toISOString(),
  });
}));

// Generate questions for research
router.post('/questions', [
  body('topic').notEmpty().withMessage('Topic is required'),
  body('questionType').optional().isIn(['research', 'safety', 'efficacy', 'dosage', 'general']).withMessage('Invalid question type'),
  body('count').optional().isInt({ min: 1, max: 20 }).withMessage('count must be between 1 and 20'),
  body('difficulty').optional().isIn(['basic', 'intermediate', 'advanced']).withMessage('Invalid difficulty level'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    topic,
    questionType = 'general',
    count = 5,
    difficulty = 'intermediate',
  } = req.body;

  const options = {
    questionType,
    count,
    difficulty,
  };

  const questions = await aiService.generateQuestions(topic, options);

  res.status(200).json({
    success: true,
    data: questions,
    timestamp: new Date().toISOString(),
  });
}));

// Validate supplement claims
router.post('/validate', [
  body('claims').isArray().withMessage('claims must be an array'),
  body('supplement').notEmpty().withMessage('supplement is required'),
  body('evidenceLevel').optional().isIn(['low', 'medium', 'high']).withMessage('Invalid evidence level'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    claims,
    supplement,
    evidenceLevel = 'medium',
  } = req.body;

  const options = {
    evidenceLevel,
  };

  const validation = await aiService.validateClaims(claims, supplement, options);

  res.status(200).json({
    success: true,
    data: validation,
    timestamp: new Date().toISOString(),
  });
}));

// Get available AI models
router.get('/models', catchAsync(async (req: Request, res: Response) => {
  const models = await aiService.getAvailableModels();

  res.status(200).json({
    success: true,
    data: models,
    timestamp: new Date().toISOString(),
  });
}));

// Get AI service statistics
router.get('/stats', catchAsync(async (req: Request, res: Response) => {
  const stats = await aiService.getStats();

  res.status(200).json({
    success: true,
    data: stats,
    timestamp: new Date().toISOString(),
  });
}));

// Chat with AI assistant
router.post('/chat', [
  body('message').notEmpty().withMessage('Message is required'),
  body('context').optional().isArray().withMessage('Context must be an array'),
  body('model').optional().isString().withMessage('Model must be a string'),
  body('temperature').optional().isFloat({ min: 0, max: 2 }).withMessage('Temperature must be between 0 and 2'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    message,
    context = [],
    model,
    temperature,
  } = req.body;

  const options = {
    context,
    model,
    temperature,
  };

  const response = await aiService.chat(message, options);

  res.status(200).json({
    success: true,
    data: response,
    timestamp: new Date().toISOString(),
  });
}));

export { router as aiRoutes };
