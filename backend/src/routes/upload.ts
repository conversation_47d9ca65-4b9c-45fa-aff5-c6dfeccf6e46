import { Router, Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { catchAsync, ValidationError } from '@/middleware/errorHandler';
import { config } from '@/config/environment';
import { UploadService } from '@/services/UploadService';

const router = Router();
const uploadService = new UploadService();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = config.upload.uploadDir;
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error, uploadDir);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}-${uniqueSuffix}${ext}`);
  },
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Allowed file types
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/csv',
    'application/json',
    'application/xml',
    'text/xml',
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ValidationError(`File type ${file.mimetype} is not allowed`));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 10, // Maximum 10 files per request
  },
});

// Upload single file
router.post('/file', upload.single('file'), catchAsync(async (req: Request, res: Response) => {
  if (!req.file) {
    throw new ValidationError('No file uploaded');
  }

  const {
    extractText = 'true',
    addToKnowledgeBase = 'false',
    source,
    metadata,
  } = req.body;

  const options = {
    extractText: extractText === 'true',
    addToKnowledgeBase: addToKnowledgeBase === 'true',
    source,
    metadata: metadata ? JSON.parse(metadata) : {},
  };

  const result = await uploadService.processFile(req.file, options);

  res.status(201).json({
    success: true,
    data: result,
    message: 'File uploaded and processed successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Upload multiple files
router.post('/files', upload.array('files', 10), catchAsync(async (req: Request, res: Response) => {
  const files = req.files as Express.Multer.File[];
  
  if (!files || files.length === 0) {
    throw new ValidationError('No files uploaded');
  }

  const {
    extractText = 'true',
    addToKnowledgeBase = 'false',
    source,
    metadata,
  } = req.body;

  const options = {
    extractText: extractText === 'true',
    addToKnowledgeBase: addToKnowledgeBase === 'true',
    source,
    metadata: metadata ? JSON.parse(metadata) : {},
  };

  const results = await uploadService.processFiles(files, options);

  res.status(201).json({
    success: true,
    data: results,
    message: `${files.length} files uploaded and processed successfully`,
    timestamp: new Date().toISOString(),
  });
}));

// Upload from URL
router.post('/url', catchAsync(async (req: Request, res: Response) => {
  const {
    url,
    extractText = true,
    addToKnowledgeBase = false,
    source,
    metadata = {},
  } = req.body;

  if (!url) {
    throw new ValidationError('URL is required');
  }

  const options = {
    extractText,
    addToKnowledgeBase,
    source,
    metadata,
  };

  const result = await uploadService.processUrl(url, options);

  res.status(201).json({
    success: true,
    data: result,
    message: 'URL content processed successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Get uploaded file
router.get('/files/:id', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const file = await uploadService.getFile(id);

  res.status(200).json({
    success: true,
    data: file,
    timestamp: new Date().toISOString(),
  });
}));

// Download file
router.get('/files/:id/download', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const file = await uploadService.getFile(id);

  if (!file.filePath || !await uploadService.fileExists(file.filePath)) {
    throw new ValidationError('File not found on disk');
  }

  res.download(file.filePath, file.originalName);
}));

// Get file content/text
router.get('/files/:id/content', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const content = await uploadService.getFileContent(id);

  res.status(200).json({
    success: true,
    data: content,
    timestamp: new Date().toISOString(),
  });
}));

// List uploaded files
router.get('/files', catchAsync(async (req: Request, res: Response) => {
  const {
    page = 1,
    limit = 20,
    type,
    source,
    search,
  } = req.query;

  const options = {
    page: parseInt(page as string),
    limit: parseInt(limit as string),
    type: type as string,
    source: source as string,
    search: search as string,
  };

  const result = await uploadService.listFiles(options);

  res.status(200).json({
    success: true,
    data: result,
    timestamp: new Date().toISOString(),
  });
}));

// Delete file
router.delete('/files/:id', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  await uploadService.deleteFile(id);

  res.status(200).json({
    success: true,
    message: 'File deleted successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Batch process files
router.post('/batch', catchAsync(async (req: Request, res: Response) => {
  const {
    fileIds,
    operation,
    options = {},
  } = req.body;

  if (!fileIds || !Array.isArray(fileIds)) {
    throw new ValidationError('fileIds must be an array');
  }

  if (!operation) {
    throw new ValidationError('operation is required');
  }

  const result = await uploadService.batchProcess(fileIds, operation, options);

  res.status(200).json({
    success: true,
    data: result,
    message: `Batch operation ${operation} completed`,
    timestamp: new Date().toISOString(),
  });
}));

// Get upload statistics
router.get('/stats', catchAsync(async (req: Request, res: Response) => {
  const stats = await uploadService.getStats();

  res.status(200).json({
    success: true,
    data: stats,
    timestamp: new Date().toISOString(),
  });
}));

// Error handling for multer
router.use((error: any, req: Request, res: Response, next: any) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      throw new ValidationError('File too large');
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      throw new ValidationError('Too many files');
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      throw new ValidationError('Unexpected file field');
    }
  }
  next(error);
});

export { router as uploadRoutes };
