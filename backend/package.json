{"name": "suplementor-backend", "version": "1.0.0", "description": "Backend API for Suplementor Knowledge Graph", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json}\"", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "docker:build": "docker build -t suplementor-backend .", "docker:run": "docker run -p 3000:3000 suplementor-backend"}, "keywords": ["knowledge-graph", "supplements", "neo4j", "ai", "gemini", "rag", "typescript", "express"], "author": "Suplementor Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "neo4j-driver": "^5.15.0", "weaviate-ts-client": "^1.5.0", "redis": "^4.6.10", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "@google/generative-ai": "^0.2.1", "openai": "^4.20.1", "langchain": "^0.0.208", "@langchain/openai": "^0.0.14", "@langchain/community": "^0.0.20", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "cheerio": "^1.0.0-rc.12", "axios": "^1.6.2", "node-cron": "^3.0.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.3.1", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "sharp": "^0.33.1", "node-fetch": "^3.3.2", "form-data": "^4.0.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/multer": "^1.4.11", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "tsx": "^4.6.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "rimraf": "^5.0.5", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "env": {"node": true, "es2022": true}, "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/explicit-function-return-type": "warn", "no-console": "warn"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false}}