#!/usr/bin/env python3
"""
Crawl4AI Wrapper Script for Suplementor Knowledge Graph
Integrates with the ottomator-agents/crawl4AI-agent-v2 functionality
"""

import asyncio
import json
import argparse
import sys
import os
import time
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
import re

# Add the crawl4AI agent path to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../ottomator-agents/crawl4AI-agent-v2'))

try:
    from crawl4ai import AsyncWebCrawler
    from crawl4ai.extraction_strategy import LLMExtractionStrategy
    from crawl4ai.chunking_strategy import RegexChunking
except ImportError:
    print("Error: crawl4ai not installed. Please install with: pip install crawl4ai")
    sys.exit(1)

class SupplementCrawler:
    def __init__(self, chunk_size: int = 1000):
        self.chunk_size = chunk_size
        self.crawler = None
        
    async def __aenter__(self):
        self.crawler = AsyncWebCrawler(verbose=True)
        await self.crawler.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.crawler:
            await self.crawler.__aexit__(exc_type, exc_val, exc_tb)

    async def crawl_single_page(self, url: str, extract_images: bool = False) -> Dict[str, Any]:
        """Crawl a single page and extract content"""
        try:
            # Configure extraction strategy for supplements
            extraction_strategy = LLMExtractionStrategy(
                provider="openai",
                api_token=os.getenv("OPENAI_API_KEY", ""),
                instruction="""
                Extract supplement and medical information from this page:
                - Supplement names and types
                - Active ingredients
                - Dosage information
                - Benefits and effects
                - Side effects and warnings
                - Drug interactions
                - Clinical studies mentioned
                
                Format as JSON with clear categories.
                """
            )
            
            # Configure chunking strategy
            chunking_strategy = RegexChunking(
                patterns=[r'\n\n', r'\n', r'\.'],
                chunk_size=self.chunk_size
            )
            
            result = await self.crawler.arun(
                url=url,
                extraction_strategy=extraction_strategy,
                chunking_strategy=chunking_strategy,
                bypass_cache=True,
                include_raw_html=False
            )
            
            # Process the result
            processed_result = self._process_crawl_result(result, url)
            return processed_result
            
        except Exception as e:
            return {
                "url": url,
                "title": "",
                "content": "",
                "chunks": [],
                "metadata": {},
                "error": str(e)
            }

    async def crawl_recursive(self, base_url: str, max_depth: int = 2, max_pages: int = 50) -> List[Dict[str, Any]]:
        """Crawl website recursively following internal links"""
        try:
            visited_urls = set()
            results = []
            urls_to_crawl = [(base_url, 0)]  # (url, depth)
            
            base_domain = urlparse(base_url).netloc
            
            while urls_to_crawl and len(results) < max_pages:
                current_url, depth = urls_to_crawl.pop(0)
                
                if current_url in visited_urls or depth > max_depth:
                    continue
                    
                visited_urls.add(current_url)
                
                # Crawl current page
                page_result = await self.crawl_single_page(current_url)
                results.append(page_result)
                
                # Extract links for next level (if not at max depth)
                if depth < max_depth:
                    try:
                        link_result = await self.crawler.arun(
                            url=current_url,
                            bypass_cache=True,
                            include_raw_html=True
                        )
                        
                        links = self._extract_internal_links(link_result.raw_html, current_url, base_domain)
                        for link in links:
                            if link not in visited_urls:
                                urls_to_crawl.append((link, depth + 1))
                                
                    except Exception as e:
                        print(f"Failed to extract links from {current_url}: {e}")
                
                # Small delay to be respectful
                await asyncio.sleep(1)
            
            return results
            
        except Exception as e:
            return [{
                "url": base_url,
                "title": "",
                "content": "",
                "chunks": [],
                "metadata": {},
                "error": str(e)
            }]

    async def crawl_sitemap(self, sitemap_url: str, max_pages: int = 100) -> List[Dict[str, Any]]:
        """Crawl URLs from sitemap"""
        try:
            # First, get the sitemap
            sitemap_result = await self.crawler.arun(
                url=sitemap_url,
                bypass_cache=True
            )
            
            # Extract URLs from sitemap
            urls = self._extract_sitemap_urls(sitemap_result.cleaned_html)
            
            # Limit number of URLs
            urls = urls[:max_pages]
            
            # Crawl each URL
            results = []
            for url in urls:
                try:
                    page_result = await self.crawl_single_page(url)
                    results.append(page_result)
                    
                    # Small delay
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    print(f"Failed to crawl {url}: {e}")
                    
            return results
            
        except Exception as e:
            return [{
                "url": sitemap_url,
                "title": "",
                "content": "",
                "chunks": [],
                "metadata": {},
                "error": str(e)
            }]

    async def crawl_markdown(self, url: str) -> Dict[str, Any]:
        """Crawl markdown/text content (like llms.txt)"""
        try:
            result = await self.crawler.arun(
                url=url,
                bypass_cache=True
            )
            
            # Process markdown content
            content = result.cleaned_html or result.markdown or ""
            chunks = self._chunk_markdown_content(content)
            
            return {
                "url": url,
                "title": self._extract_title_from_content(content),
                "content": content,
                "chunks": chunks,
                "metadata": {
                    "title": self._extract_title_from_content(content),
                    "domain": urlparse(url).netloc,
                    "content_type": "markdown"
                }
            }
            
        except Exception as e:
            return {
                "url": url,
                "title": "",
                "content": "",
                "chunks": [],
                "metadata": {},
                "error": str(e)
            }

    def _process_crawl_result(self, result, url: str) -> Dict[str, Any]:
        """Process crawl result into standardized format"""
        content = result.cleaned_html or result.markdown or ""
        
        # Extract metadata
        metadata = {
            "title": result.metadata.get("title", "") if result.metadata else "",
            "description": result.metadata.get("description", "") if result.metadata else "",
            "domain": urlparse(url).netloc,
            "content_length": len(content)
        }
        
        # Create chunks
        chunks = []
        if hasattr(result, 'extracted_content') and result.extracted_content:
            # Use extracted chunks if available
            for i, chunk in enumerate(result.extracted_content):
                chunks.append({
                    "id": f"chunk_{i}",
                    "content": chunk.get("content", ""),
                    "headers": chunk.get("headers", []),
                    "wordCount": len(chunk.get("content", "").split()),
                    "charCount": len(chunk.get("content", "")),
                    "chunkIndex": i
                })
        else:
            # Create simple chunks
            chunks = self._chunk_content(content)
        
        return {
            "url": url,
            "title": metadata["title"],
            "content": content,
            "chunks": chunks,
            "metadata": metadata
        }

    def _chunk_content(self, content: str) -> List[Dict[str, Any]]:
        """Create content chunks"""
        chunks = []
        words = content.split()
        
        chunk_size_words = self.chunk_size // 5  # Approximate words per chunk
        
        for i in range(0, len(words), chunk_size_words):
            chunk_words = words[i:i + chunk_size_words]
            chunk_content = " ".join(chunk_words)
            
            chunks.append({
                "id": f"chunk_{i // chunk_size_words}",
                "content": chunk_content,
                "headers": [],
                "wordCount": len(chunk_words),
                "charCount": len(chunk_content),
                "chunkIndex": i // chunk_size_words
            })
        
        return chunks

    def _chunk_markdown_content(self, content: str) -> List[Dict[str, Any]]:
        """Chunk markdown content by headers"""
        chunks = []
        lines = content.split('\n')
        current_chunk = []
        current_headers = []
        chunk_index = 0
        
        for line in lines:
            if line.startswith('#'):
                # New header found, save previous chunk
                if current_chunk:
                    chunk_content = '\n'.join(current_chunk)
                    chunks.append({
                        "id": f"chunk_{chunk_index}",
                        "content": chunk_content,
                        "headers": current_headers.copy(),
                        "wordCount": len(chunk_content.split()),
                        "charCount": len(chunk_content),
                        "chunkIndex": chunk_index
                    })
                    chunk_index += 1
                
                # Start new chunk
                current_chunk = [line]
                header_level = len(line.split()[0])
                current_headers = current_headers[:header_level-1] + [line.strip('# ')]
            else:
                current_chunk.append(line)
        
        # Add final chunk
        if current_chunk:
            chunk_content = '\n'.join(current_chunk)
            chunks.append({
                "id": f"chunk_{chunk_index}",
                "content": chunk_content,
                "headers": current_headers.copy(),
                "wordCount": len(chunk_content.split()),
                "charCount": len(chunk_content),
                "chunkIndex": chunk_index
            })
        
        return chunks

    def _extract_internal_links(self, html: str, base_url: str, base_domain: str) -> List[str]:
        """Extract internal links from HTML"""
        links = []
        
        # Simple regex to find links
        link_pattern = r'href=["\']([^"\']+)["\']'
        matches = re.findall(link_pattern, html)
        
        for match in matches:
            # Convert relative URLs to absolute
            full_url = urljoin(base_url, match)
            
            # Check if it's an internal link
            if urlparse(full_url).netloc == base_domain:
                # Remove fragments
                clean_url = full_url.split('#')[0]
                if clean_url not in links:
                    links.append(clean_url)
        
        return links

    def _extract_sitemap_urls(self, sitemap_content: str) -> List[str]:
        """Extract URLs from sitemap XML"""
        urls = []
        
        # Simple regex to find URLs in sitemap
        url_pattern = r'<loc>([^<]+)</loc>'
        matches = re.findall(url_pattern, sitemap_content)
        
        for match in matches:
            urls.append(match.strip())
        
        return urls

    def _extract_title_from_content(self, content: str) -> str:
        """Extract title from content"""
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                return line[:100]  # First non-header line
            elif line.startswith('# '):
                return line[2:].strip()
        
        return "Untitled"

async def main():
    parser = argparse.ArgumentParser(description='Crawl4AI Wrapper for Supplement Research')
    parser.add_argument('--url', required=True, help='URL to crawl')
    parser.add_argument('--type', choices=['single_page', 'recursive', 'sitemap', 'llms_txt'], 
                       default='single_page', help='Crawl type')
    parser.add_argument('--output', required=True, help='Output file path')
    parser.add_argument('--crawl-id', required=True, help='Crawl ID for tracking')
    parser.add_argument('--max-depth', type=int, default=2, help='Max crawl depth')
    parser.add_argument('--max-pages', type=int, default=50, help='Max pages to crawl')
    parser.add_argument('--chunk-size', type=int, default=1000, help='Chunk size')
    parser.add_argument('--extract-images', action='store_true', help='Extract images')
    parser.add_argument('--batch-mode', action='store_true', help='Batch mode for multiple results')
    
    args = parser.parse_args()
    
    try:
        async with SupplementCrawler(chunk_size=args.chunk_size) as crawler:
            if args.type == 'single_page':
                result = await crawler.crawl_single_page(args.url, args.extract_images)
                results = result if args.batch_mode else [result]
            elif args.type == 'recursive':
                results = await crawler.crawl_recursive(args.url, args.max_depth, args.max_pages)
            elif args.type == 'sitemap':
                results = await crawler.crawl_sitemap(args.url, args.max_pages)
            elif args.type == 'llms_txt':
                result = await crawler.crawl_markdown(args.url)
                results = result if args.batch_mode else [result]
            else:
                raise ValueError(f"Unknown crawl type: {args.type}")
            
            # Save results
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"Crawl completed successfully. Results saved to {args.output}")
            
    except Exception as e:
        error_result = {
            "url": args.url,
            "title": "",
            "content": "",
            "chunks": [],
            "metadata": {},
            "error": str(e)
        }
        
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump([error_result] if args.batch_mode else error_result, f, indent=2)
        
        print(f"Crawl failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
