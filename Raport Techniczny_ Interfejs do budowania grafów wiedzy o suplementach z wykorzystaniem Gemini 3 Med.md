# Raport Techniczny: Interfejs do budowania grafów wiedzy o suplementach z wykorzystaniem Gemini 3 Med

## Spis treści
1. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](#wprowadzenie)
2. [<PERSON><PERSON><PERSON> wym<PERSON>](#analiza-wymagań)
3. [Przegląd wybranych projektów GitHub](#przegląd-wybranych-projektów-github)
   - [Weizhe-Chen/KnowledgeGraph](#weizhe-chenknowledgegraph)
   - [robert-mcdermott/ai-knowledge-graph](#robert-mcdermottai-knowledge-graph)
   - [WSE-research/KinGVisher-Knowledge-Graph-Visualizer](#wse-researchkingvisher-knowledge-graph-visualizer)
   - [mjherich/memory-visualizer](#mjherichmemory-visualizer)
   - [semantic-systems/coypu-kg-vis](#semantic-systemscoypu-kg-vis)
4. [Integracja z Gemini 3 Med](#integracja-z-gemini-3-med)
   - [Możliwości modelu Gemini 3 Med](#możliwości-modelu-gemini-3-med)
   - [Strategie integracji](#strategie-integracji)
   - [Przykłady implementacji](#przykłady-implementacji)
5. [Proponowana architektura rozwiązania](#proponowana-architektura-rozwiązania)
   - [Komponenty systemu](#komponenty-systemu)
   - [Przepływ danych](#przepływ-danych)
   - [Technologie](#technologie)
6. [Rekomendacje implementacyjne](#rekomendacje-implementacyjne)
   - [Backend](#backend)
   - [Frontend](#frontend)
   - [Integracja z bazą wiedzy o suplementach](#integracja-z-bazą-wiedzy-o-suplementach)
7. [Wnioski](#wnioski)
8. [Bibliografia](#bibliografia)

## Wprowadzenie

Niniejszy raport techniczny przedstawia analizę i rekomendacje dotyczące budowy interfejsu wykorzystującego model Gemini 3 Med do tworzenia grafów wiedzy o suplementach. Celem projektu jest stworzenie systemu, który będzie optymalnie reprezentował informacje dla ludzkiego zrozumienia, podobnie jak w przykładach translator.globe.engineer i index.globe.engineer.

Główne założenia projektu:
- Tworzenie spisu treści na temat suplementów
- Wizualizacja każdej sekcji i pojęcia za pomocą optymalnych obrazów lub diagramów
- Umożliwienie szybkiego znajdowania potrzebnych informacji
- Umożliwienie odkrywania nowych, nieznanych wcześniej informacji

Raport zawiera przegląd wybranych projektów z GitHuba, które mogą posłużyć jako podstawa do budowy takiego interfejsu, analizę możliwości integracji z modelem Gemini 3 Med oraz propozycję architektury rozwiązania.

## Analiza wymagań

Na podstawie dostarczonych informacji i przykładów, system powinien spełniać następujące wymagania:

1. **Funkcjonalne**:
   - Generowanie grafów wiedzy na podstawie danych o suplementach
   - Interaktywna wizualizacja grafów wiedzy
   - Możliwość eksploracji i nawigacji po grafie
   - Wyszukiwanie i filtrowanie informacji
   - Automatyczne generowanie wizualizacji dla pojęć i relacji
   - Tworzenie spisu treści dla łatwiejszej nawigacji

2. **Niefunkcjonalne**:
   - Intuicyjny interfejs użytkownika
   - Responsywność i wydajność
   - Skalowalność dla dużych zbiorów danych
   - Możliwość rozbudowy i dostosowania
   - Kompatybilność z różnymi przeglądarkami

3. **Techniczne**:
   - Integracja z API Gemini 3 Med
   - Przechowywanie i zarządzanie grafami wiedzy
   - Mechanizmy wizualizacji grafów
   - Przetwarzanie języka naturalnego do analizy i kategoryzacji informacji

## Przegląd wybranych projektów GitHub

### Weizhe-Chen/KnowledgeGraph

**URL**: [https://github.com/Weizhe-Chen/KnowledgeGraph](https://github.com/Weizhe-Chen/KnowledgeGraph)

**Opis**: Projekt KnowledgeGraph to narzędzie do organizowania i udostępniania wiedzy online. Umożliwia śledzenie tego, czego się nauczyłeś, i dzielenie się swoją wiedzą ze światem. Interaktywny graf pozwala na łatwą nawigację między różnymi pojęciami i relacjami.

**Kluczowe funkcje**:
- Interaktywny graf wiedzy z możliwością nawigacji
- Konwersja plików Markdown do HTML za pomocą Pandoc
- Generowanie grafu za pomocą Graphviz
- Możliwość udostępniania grafu online poprzez GitHub Pages

**Technologie**:
- Pandoc (konwerter dokumentów)
- Graphviz (wizualizacja grafów)
- HTML/CSS/JavaScript
- Markdown

**Zalety**:
- Prosty w użyciu i konfiguracji
- Możliwość łatwego udostępniania online
- Dobra dokumentacja
- Lekki i szybki

**Wady**:
- Ograniczone możliwości wizualizacji
- Brak wbudowanej integracji z modelami językowymi
- Wymaga ręcznego tworzenia plików Markdown
- Ograniczone możliwości skalowania dla dużych grafów

**Potencjał integracji z Gemini 3 Med**: Średni. Projekt może służyć jako podstawa do wizualizacji, ale wymaga znaczących modyfikacji do integracji z Gemini 3 Med i automatycznego generowania grafów wiedzy o suplementach.

### robert-mcdermott/ai-knowledge-graph

**URL**: [https://github.com/robert-mcdermott/ai-knowledge-graph](https://github.com/robert-mcdermott/ai-knowledge-graph)

**Opis**: AI Powered Knowledge Graph Generator to narzędzie do generowania grafów wiedzy z wykorzystaniem modeli językowych. Projekt jest szczególnie interesujący ze względu na wbudowaną integrację z modelami językowymi i możliwość automatycznego generowania grafów wiedzy na podstawie tekstu.

**Kluczowe funkcje**:
- Generowanie grafów wiedzy z wykorzystaniem modeli językowych
- Standaryzacja encji
- Wnioskowanie relacji
- Wizualizacja grafów za pomocą PyVis
- Eksport do formatów HTML i JSON

**Technologie**:
- Python
- NetworkX (biblioteka do analizy grafów)
- PyVis (wizualizacja grafów)
- Integracja z modelami językowymi (w tym możliwość wykorzystania Gemini)

**Zalety**:
- Gotowa integracja z modelami językowymi
- Automatyczne generowanie grafów wiedzy
- Możliwość dostosowania do różnych domen
- Aktywnie rozwijany projekt z dobrym wsparciem

**Wady**:
- Wymaga dostosowania do specyfiki suplementów
- Interfejs użytkownika wymaga rozbudowy
- Może wymagać optymalizacji dla dużych zbiorów danych

**Potencjał integracji z Gemini 3 Med**: Wysoki. Projekt już zawiera integrację z modelami językowymi i może być łatwo dostosowany do pracy z Gemini 3 Med. Konfiguracja w pliku config.toml pozwala na łatwą zmianę modelu.

### WSE-research/KinGVisher-Knowledge-Graph-Visualizer

**URL**: [https://github.com/WSE-research/KinGVisher-Knowledge-Graph-Visualizer](https://github.com/WSE-research/KinGVisher-Knowledge-Graph-Visualizer)

**Opis**: KinGVisher to narzędzie do wizualizacji grafów wiedzy w przeglądarce internetowej. Wykorzystuje istniejące endpointy triplestorów RDF (np. DBpedia, Wikidata) do pobierania danych i ich wizualizacji. Zapewnia przyjazny dla użytkownika interfejs do eksploracji danych i tworzenia wizualizacji.

**Kluczowe funkcje**:
- Wizualizacja grafów wiedzy w przeglądarce
- Integracja z endpointami SPARQL
- Definiowanie zasobów jako punktów startowych eksploracji grafu
- Listy białe i czarne dla predykatów
- Style wizualne dla węzłów i krawędzi

**Technologie**:
- Streamlit (framework do tworzenia aplikacji webowych)
- Biblioteka agraph (wrapper dla vis.js)
- Python
- SPARQL

**Zalety**:
- Gotowy interfejs użytkownika
- Integracja z istniejącymi źródłami danych RDF
- Możliwość dostosowania wizualizacji
- Łatwe wdrożenie za pomocą Dockera

**Wady**:
- Brak wbudowanej integracji z modelami językowymi
- Wymaga dostępu do endpointu SPARQL
- Może wymagać dostosowania do specyfiki suplementów

**Potencjał integracji z Gemini 3 Med**: Średni. Projekt oferuje dobry interfejs do wizualizacji, ale wymaga dodatkowej pracy nad integracją z Gemini 3 Med i tworzeniem grafów wiedzy o suplementach.

### mjherich/memory-visualizer

**URL**: [https://github.com/mjherich/memory-visualizer](https://github.com/mjherich/memory-visualizer)

**Opis**: Memory Visualizer to interaktywny wizualizator grafów wiedzy dla protokołu MCP (Model Context Protocol) firmy Anthropic. Umożliwia eksplorację, debugowanie i analizę encji, relacji i obserwacji z plików memory.json.

**Kluczowe funkcje**:
- Interaktywna wizualizacja grafów wiedzy za pomocą D3.js
- Filtrowanie według typów encji i relacji
- Wyszukiwanie w encjach i relacjach
- Szczegółowy panel informacyjny
- Przeciąganie i upuszczanie plików memory.json

**Technologie**:
- React + TypeScript
- D3.js (wizualizacja)
- Vite
- TailwindCSS

**Zalety**:
- Nowoczesny i interaktywny interfejs
- Dobra wydajność nawet dla dużych grafów
- Możliwość filtrowania i wyszukiwania
- Szczegółowy panel informacyjny

**Wady**:
- Specyficzny dla formatu memory.json
- Wymaga dostosowania do innych formatów danych
- Brak wbudowanej integracji z modelami językowymi

**Potencjał integracji z Gemini 3 Med**: Średni. Projekt oferuje świetny interfejs do wizualizacji, ale wymaga dostosowania do pracy z Gemini 3 Med i formatem danych o suplementach.

### semantic-systems/coypu-kg-vis

**URL**: [https://github.com/semantic-systems/coypu-kg-vis](https://github.com/semantic-systems/coypu-kg-vis)

**Opis**: Coypu-kg-vis to narzędzie do wizualizacji i eksploracji grafów wiedzy online. Celem jest zapewnienie intuicyjnego i przyjaznego dla użytkownika interfejsu do eksploracji grafów wiedzy i odkrywania relacji.

**Kluczowe funkcje**:
- Intuicyjny interfejs do interakcji z grafami wiedzy
- Inteligentne wyszukiwanie do dodawania nowych węzłów
- Filtrowanie dla skupienia się na istotnych danych
- Wsparcie dla SPARQL do importu i eksportu danych

**Technologie**:
- Svelte (62.8%)
- TypeScript (34.7%)
- JavaScript (1.5%)

**Zalety**:
- Nowoczesny i intuicyjny interfejs
- Wsparcie dla SPARQL
- Możliwość filtrowania i wyszukiwania
- Skróty klawiaturowe dla szybszej pracy

**Wady**:
- Brak wbudowanej integracji z modelami językowymi
- Wymaga dostosowania do specyfiki suplementów

**Potencjał integracji z Gemini 3 Med**: Średni. Projekt oferuje dobry interfejs do wizualizacji, ale wymaga dodatkowej pracy nad integracją z Gemini 3 Med.

## Integracja z Gemini 3 Med

### Możliwości modelu Gemini 3 Med

Gemini 3 Med to zaawansowany model językowy specjalizujący się w dziedzinie medycznej, co czyni go idealnym kandydatem do pracy z danymi o suplementach. Model ten oferuje następujące możliwości:

1. **Analiza i ekstrakcja informacji** z tekstów medycznych i naukowych
2. **Identyfikacja encji** (np. suplementy, składniki, efekty zdrowotne)
3. **Wykrywanie relacji** między encjami (np. "suplement X zawiera składnik Y", "składnik Y ma wpływ na układ Z")
4. **Generowanie strukturyzowanych danych** na podstawie tekstu
5. **Odpowiadanie na pytania** dotyczące suplementów i ich działania

### Strategie integracji

Istnieje kilka strategii integracji modelu Gemini 3 Med z systemem grafów wiedzy:

1. **Ekstrakcja wiedzy z tekstu**:
   - Wykorzystanie Gemini 3 Med do analizy tekstów o suplementach
   - Ekstrakcja encji (suplementy, składniki, efekty)
   - Identyfikacja relacji między encjami
   - Generowanie trójek RDF (podmiot-predykat-obiekt)

2. **Generowanie zapytań SPARQL**:
   - Konwersja pytań w języku naturalnym na zapytania SPARQL
   - Wykonywanie zapytań na grafie wiedzy
   - Prezentacja wyników w przyjaznej formie

3. **Wzbogacanie grafu wiedzy**:
   - Automatyczne uzupełnianie brakujących informacji
   - Sugerowanie nowych połączeń między encjami
   - Weryfikacja istniejących relacji

4. **Generowanie wizualizacji**:
   - Tworzenie opisów dla węzłów i krawędzi
   - Generowanie optymalnych wizualizacji dla pojęć
   - Tworzenie spisu treści i kategoryzacja informacji

### Przykłady implementacji

#### Przykład 1: Ekstrakcja wiedzy z tekstu za pomocą Gemini 3 Med

```python
import sys
sys.path.append('/opt/.manus/.sandbox-runtime')
from data_api import ApiClient
import json

def extract_knowledge_from_text(text):
    client = ApiClient()
    
    # Przygotowanie promptu dla Gemini 3 Med
    prompt = f"""
    Przeanalizuj poniższy tekst o suplementach i wyodrębnij:
    1. Nazwy suplementów
    2. Składniki aktywne
    3. Efekty zdrowotne
    4. Relacje między nimi (np. "suplement zawiera składnik", "składnik ma efekt")
    
    Zwróć wyniki w formacie JSON:
    {{
        "entities": [
            {{"id": "entity_id", "type": "supplement|ingredient|effect", "name": "nazwa"}}
        ],
        "relations": [
            {{"source": "entity_id_1", "target": "entity_id_2", "type": "contains|has_effect"}}
        ]
    }}
    
    Tekst do analizy:
    {text}
    """
    
    # Wywołanie API Gemini 3 Med
    response = client.call_api('GoogleAI/generate_content', query={
        'model': 'gemini-3-med',
        'prompt': prompt,
        'temperature': 0.2,
        'max_output_tokens': 2048
    })
    
    # Parsowanie odpowiedzi
    try:
        knowledge_graph = json.loads(response['text'])
        return knowledge_graph
    except:
        return {"entities": [], "relations": []}

# Przykład użycia
text = """
Witamina D3 (cholekalcyferol) jest suplementem, który pomaga w utrzymaniu zdrowych kości i zębów.
Zawiera aktywną formę witaminy D, która wspomaga wchłanianie wapnia i fosforu.
Niedobór witaminy D może prowadzić do osteoporozy i osłabienia mięśni.
"""

knowledge_graph = extract_knowledge_from_text(text)
print(json.dumps(knowledge_graph, indent=2))
```

#### Przykład 2: Generowanie zapytań SPARQL na podstawie pytań w języku naturalnym

```python
import sys
sys.path.append('/opt/.manus/.sandbox-runtime')
from data_api import ApiClient
import json

def generate_sparql_query(question):
    client = ApiClient()
    
    # Przygotowanie promptu dla Gemini 3 Med
    prompt = f"""
    Przekształć poniższe pytanie dotyczące suplementów na zapytanie SPARQL.
    Załóż, że mamy graf wiedzy z następującymi typami encji:
    - Supplement (suplement)
    - Ingredient (składnik)
    - Effect (efekt zdrowotny)
    
    I następującymi relacjami:
    - contains (suplement zawiera składnik)
    - has_effect (składnik ma efekt zdrowotny)
    - interacts_with (suplement wchodzi w interakcję z innym suplementem)
    
    Pytanie: {question}
    
    Zwróć tylko zapytanie SPARQL bez dodatkowych wyjaśnień.
    """
    
    # Wywołanie API Gemini 3 Med
    response = client.call_api('GoogleAI/generate_content', query={
        'model': 'gemini-3-med',
        'prompt': prompt,
        'temperature': 0.1,
        'max_output_tokens': 1024
    })
    
    # Zwrócenie wygenerowanego zapytania SPARQL
    return response['text'].strip()

# Przykład użycia
question = "Jakie suplementy zawierają witaminę D3 i jakie mają efekty zdrowotne?"
sparql_query = generate_sparql_query(question)
print(sparql_query)
```

#### Przykład 3: Wzbogacanie grafu wiedzy o nowe informacje

```python
import sys
sys.path.append('/opt/.manus/.sandbox-runtime')
from data_api import ApiClient
import json
import networkx as nx

def enrich_knowledge_graph(graph_data):
    client = ApiClient()
    
    # Konwersja danych grafu do formatu tekstowego
    graph_text = json.dumps(graph_data, indent=2)
    
    # Przygotowanie promptu dla Gemini 3 Med
    prompt = f"""
    Przeanalizuj poniższy graf wiedzy o suplementach i zaproponuj:
    1. Nowe encje, które powinny zostać dodane
    2. Nowe relacje między istniejącymi encjami
    3. Dodatkowe informacje o istniejących encjach
    
    Graf wiedzy:
    {graph_text}
    
    Zwróć wyniki w formacie JSON:
    {{
        "new_entities": [
            {{"id": "entity_id", "type": "supplement|ingredient|effect", "name": "nazwa"}}
        ],
        "new_relations": [
            {{"source": "entity_id_1", "target": "entity_id_2", "type": "contains|has_effect"}}
        ],
        "entity_enrichments": [
            {{"id": "entity_id", "property": "nazwa_właściwości", "value": "wartość"}}
        ]
    }}
    """
    
    # Wywołanie API Gemini 3 Med
    response = client.call_api('GoogleAI/generate_content', query={
        'model': 'gemini-3-med',
        'prompt': prompt,
        'temperature': 0.3,
        'max_output_tokens': 2048
    })
    
    # Parsowanie odpowiedzi
    try:
        enrichments = json.loads(response['text'])
        return enrichments
    except:
        return {"new_entities": [], "new_relations": [], "entity_enrichments": []}

# Przykład użycia
graph_data = {
    "entities": [
        {"id": "vit_d3", "type": "supplement", "name": "Witamina D3"},
        {"id": "calcium", "type": "ingredient", "name": "Wapń"},
        {"id": "bone_health", "type": "effect", "name": "Zdrowie kości"}
    ],
    "relations": [
        {"source": "vit_d3", "target": "calcium", "type": "contains"},
        {"source": "calcium", "target": "bone_health", "type": "has_effect"}
    ]
}

enrichments = enrich_knowledge_graph(graph_data)
print(json.dumps(enrichments, indent=2))
```

## Proponowana architektura rozwiązania

### Komponenty systemu

Proponowana architektura systemu składa się z następujących komponentów:

1. **Backend**:
   - **API Gateway** - punkt wejścia do systemu, zarządzanie zapytaniami
   - **Knowledge Graph Service** - zarządzanie grafem wiedzy, wykonywanie zapytań
   - **Gemini 3 Med Integration** - integracja z API Gemini 3 Med
   - **Data Processing Service** - przetwarzanie danych, ekstrakcja wiedzy
   - **Storage Service** - przechowywanie grafów wiedzy i metadanych

2. **Frontend**:
   - **User Interface** - interfejs użytkownika, wizualizacja grafów
   - **Search & Navigation** - wyszukiwanie i nawigacja po grafie
   - **Visualization Engine** - silnik wizualizacji grafów
   - **Content Renderer** - renderowanie treści i wizualizacji

3. **Baza danych**:
   - **Graph Database** - baza danych grafowa (np. Neo4j, Neptune)
   - **Document Store** - przechowywanie dokumentów i metadanych

### Przepływ danych

1. **Pozyskiwanie danych**:
   - Importowanie danych o suplementach z różnych źródeł
   - Ekstrakcja wiedzy z tekstów za pomocą Gemini 3 Med
   - Generowanie trójek RDF

2. **Przetwarzanie danych**:
   - Standaryzacja encji i relacji
   - Wnioskowanie nowych relacji
   - Wzbogacanie grafu wiedzy

3. **Wizualizacja i eksploracja**:
   - Generowanie interaktywnych wizualizacji
   - Nawigacja po grafie wiedzy
   - Wyszukiwanie i filtrowanie informacji

4. **Interakcja z użytkownikiem**:
   - Odpowiadanie na pytania użytkownika
   - Generowanie spisu treści i kategoryzacja informacji
   - Tworzenie optymalnych wizualizacji dla pojęć

### Technologie

Na podstawie analizy wybranych projektów GitHub, proponujemy następujące technologie:

1. **Backend**:
   - **Język programowania**: Python
   - **Framework**: FastAPI lub Flask
   - **Baza danych grafowa**: Neo4j lub Neptune
   - **Integracja z Gemini 3 Med**: Google AI API

2. **Frontend**:
   - **Framework**: React lub Svelte
   - **Biblioteka wizualizacji**: D3.js lub vis.js
   - **Stylowanie**: TailwindCSS

3. **Narzędzia**:
   - **Zarządzanie grafem wiedzy**: NetworkX, RDFLib
   - **Wizualizacja**: PyVis, D3.js
   - **Konteneryzacja**: Docker

## Rekomendacje implementacyjne

### Backend

1. **Integracja z Gemini 3 Med**:
   - Wykorzystanie projektu `robert-mcdermott/ai-knowledge-graph` jako podstawy
   - Dostosowanie konfiguracji do pracy z Gemini 3 Med
   - Implementacja funkcji ekstrakcji wiedzy z tekstu

2. **Zarządzanie grafem wiedzy**:
   - Wykorzystanie Neo4j jako bazy danych grafowej
   - Implementacja API do zarządzania grafem wiedzy
   - Integracja z SPARQL dla zaawansowanych zapytań

3. **Przetwarzanie danych**:
   - Implementacja pipeline'u przetwarzania danych
   - Standaryzacja encji i relacji
   - Wnioskowanie nowych relacji

### Frontend

1. **Interfejs użytkownika**:
   - Wykorzystanie projektu `mjherich/memory-visualizer` lub `semantic-systems/coypu-kg-vis` jako podstawy
   - Dostosowanie interfejsu do specyfiki suplementów
   - Implementacja funkcji wyszukiwania i filtrowania

2. **Wizualizacja grafów**:
   - Wykorzystanie D3.js do interaktywnej wizualizacji
   - Implementacja różnych widoków grafu (sieć, drzewo, hierarchia)
   - Optymalizacja wydajności dla dużych grafów

3. **Generowanie treści**:
   - Wykorzystanie Gemini 3 Med do generowania opisów i wizualizacji
   - Implementacja spisu treści i kategoryzacji informacji
   - Automatyczne generowanie optymalnych wizualizacji dla pojęć

### Integracja z bazą wiedzy o suplementach

1. **Źródła danych**:
   - Publikacje naukowe o suplementach
   - Bazy danych o składnikach aktywnych
   - Informacje o efektach zdrowotnych i interakcjach

2. **Struktura grafu wiedzy**:
   - Encje: suplementy, składniki, efekty zdrowotne, badania naukowe
   - Relacje: zawiera, ma efekt, wchodzi w interakcję, jest potwierdzony przez

3. **Aktualizacja i utrzymanie**:
   - Automatyczne aktualizacje na podstawie nowych publikacji
   - Weryfikacja informacji przez Gemini 3 Med
   - Mechanizmy raportowania błędów i sugestii

## Wnioski

Na podstawie przeprowadzonej analizy, rekomendujemy następujące podejście do budowy interfejsu do grafów wiedzy o suplementach z wykorzystaniem Gemini 3 Med:

1. **Wykorzystanie projektu `robert-mcdermott/ai-knowledge-graph` jako podstawy backendu** ze względu na gotową integrację z modelami językowymi i możliwość automatycznego generowania grafów wiedzy.

2. **Wykorzystanie projektu `semantic-systems/coypu-kg-vis` lub `mjherich/memory-visualizer` jako podstawy frontendu** ze względu na nowoczesny i interaktywny interfejs do wizualizacji grafów wiedzy.

3. **Integracja z Gemini 3 Med** do ekstrakcji wiedzy z tekstu, generowania zapytań SPARQL i wzbogacania grafu wiedzy.

4. **Implementacja dedykowanych funkcji dla domeny suplementów**, takich jak analiza składników, efektów zdrowotnych i interakcji.

5. **Wykorzystanie Neo4j jako bazy danych grafowej** ze względu na dojrzałość, wydajność i bogaty ekosystem narzędzi.

Takie podejście pozwoli na stworzenie zaawansowanego interfejsu do grafów wiedzy o suplementach, który będzie optymalnie reprezentował informacje dla ludzkiego zrozumienia, zgodnie z założeniami projektu.

## Bibliografia

1. Weizhe-Chen/KnowledgeGraph - [https://github.com/Weizhe-Chen/KnowledgeGraph](https://github.com/Weizhe-Chen/KnowledgeGraph)
2. robert-mcdermott/ai-knowledge-graph - [https://github.com/robert-mcdermott/ai-knowledge-graph](https://github.com/robert-mcdermott/ai-knowledge-graph)
3. WSE-research/KinGVisher-Knowledge-Graph-Visualizer - [https://github.com/WSE-research/KinGVisher-Knowledge-Graph-Visualizer](https://github.com/WSE-research/KinGVisher-Knowledge-Graph-Visualizer)
4. mjherich/memory-visualizer - [https://github.com/mjherich/memory-visualizer](https://github.com/mjherich/memory-visualizer)
5. semantic-systems/coypu-kg-vis - [https://github.com/semantic-systems/coypu-kg-vis](https://github.com/semantic-systems/coypu-kg-vis)
6. Augmenting Gemini-1.0-Pro with Knowledge Graphs via LangChain - [https://medium.com/google-cloud/augmenting-gemini-1-0-pro-with-knowledge-graphs-via-langchain-bacc2804c423](https://medium.com/google-cloud/augmenting-gemini-1-0-pro-with-knowledge-graphs-via-langchain-bacc2804c423)
7. Automating Knowledge Graphs with SurrealDB and Gemini - [https://surrealdb.com/blog/automating-knowledge-graphs-with-surrealdb-and-gemini](https://surrealdb.com/blog/automating-knowledge-graphs-with-surrealdb-and-gemini)
8. Knowledge Graph QA Using Gemini and NebulaGraph Lite - [https://www.nebula-graph.io/posts/Knowledge_Graph_QA_using_Gemini_NebulaGraph-Lite](https://www.nebula-graph.io/posts/Knowledge_Graph_QA_using_Gemini_NebulaGraph-Lite)
9. Drug Repurposing Using Knowledge Graphs - Gemini API - [https://ai.google.dev/competition/projects/drug-repurposing-using-knowledge-graphs](https://ai.google.dev/competition/projects/drug-repurposing-using-knowledge-graphs)
10. Generative Transformation to Graph Model With Google's Gemini Pro - [https://neo4j.com/blog/developer/genai-graph-model-google-gemini-pro/](https://neo4j.com/blog/developer/genai-graph-model-google-gemini-pro/)
