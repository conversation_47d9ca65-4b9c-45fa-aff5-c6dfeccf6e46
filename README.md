# Suplementor Knowledge Graph

A comprehensive knowledge graph interface for supplement information using Gemini 3 Med and modern web technologies. This system creates interactive, iteratively expanding graphs with AI-powered knowledge extraction and RAG capabilities.

## 🎯 Features

- **Interactive Graph Visualization**: D3.js-powered force-directed graphs with zoom, pan, and filtering
- **AI-Powered Knowledge Extraction**: Gemini 3 Med integration for medical knowledge processing
- **Medical AI Research**: Gemma3-4B-Medical with Chain-of-Thought reasoning for supplement analysis
- **Advanced Web Research**: Brave Search + Tavily integration for comprehensive data collection
- **Website Crawling**: Crawl4AI integration for deep content extraction and analysis
- **RAG System**: Retrieval-Augmented Generation for intelligent information retrieval
- **Iterative Graph Expansion**: Automated crawling and LLM-based knowledge discovery
- **Supplement-Specific Entities**: Supplements, ingredients, effects, contraindications, interactions
- **Real-time Updates**: Live graph updates as new knowledge is discovered
- **ReactBits UI**: Animated, interactive components for enhanced user experience
- **Responsive Design**: Mobile-optimized interface following Atomic Design principles

## 🏗️ Architecture

### Backend
- **Runtime**: Node.js 18+ with Express.js and TypeScript
- **Graph Database**: Neo4j for storing knowledge graphs
- **Vector Database**: Weaviate for RAG embeddings and semantic search
- **Cache**: Redis for performance optimization
- **AI Integration**: Gemini 3 Med, Bielik V3, Gemma3-4b models

### Frontend
- **Framework**: React 18+ with TypeScript and Vite
- **Visualization**: D3.js for interactive graph rendering
- **Styling**: TailwindCSS with Atomic Design components
- **State Management**: React Query for server state management

### Infrastructure
- **Containerization**: Docker and Docker Compose
- **Development**: Hot reload, TypeScript compilation, automated testing

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Docker and Docker Compose
- Git

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd suplementor-knowledge-graph
```

2. **Start the development environment**
```bash
docker-compose up -d
```

3. **Install dependencies**
```bash
# Backend
cd backend
npm install

# Frontend
cd ../frontend
npm install
```

4. **Configure environment variables**
```bash
# Copy example environment files
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Edit the .env files with your API keys and database credentials
```

5. **Start development servers**
```bash
# Backend (Terminal 1)
cd backend
npm run dev

# Frontend (Terminal 2)
cd frontend
npm run dev
```

6. **Access the application**
- Frontend: http://localhost:5173
- Backend API: http://localhost:3000
- Neo4j Browser: http://localhost:7474
- Weaviate: http://localhost:8080

## 📁 Project Structure

```
suplementor-knowledge-graph/
├── backend/                    # Node.js backend
│   ├── src/
│   │   ├── controllers/       # API route handlers
│   │   ├── services/          # Business logic
│   │   ├── models/           # Data models
│   │   ├── middleware/       # Express middleware
│   │   ├── utils/            # Utility functions
│   │   └── types/            # TypeScript type definitions
│   ├── tests/                # Backend tests
│   ├── docker/               # Docker configuration
│   └── package.json
├── frontend/                  # React frontend
│   ├── src/
│   │   ├── components/       # React components
│   │   │   ├── atoms/        # Basic UI elements
│   │   │   ├── molecules/    # Component combinations
│   │   │   ├── organisms/    # Complex components
│   │   │   └── templates/    # Page layouts
│   │   ├── pages/            # Application pages
│   │   ├── services/         # API services
│   │   ├── hooks/            # Custom React hooks
│   │   ├── utils/            # Utility functions
│   │   └── types/            # TypeScript type definitions
│   ├── tests/                # Frontend tests
│   └── package.json
├── docs/                     # Documentation
├── docker-compose.yml        # Development environment
└── README.md
```

## 🔧 Development

### Backend Development
```bash
cd backend
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run tests
npm run lint         # Lint code
```

### Frontend Development
```bash
cd frontend
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run tests
npm run lint         # Lint code
```

### Database Management
```bash
# Neo4j
docker exec -it neo4j cypher-shell

# Weaviate
curl http://localhost:8080/v1/schema

# Redis
docker exec -it redis redis-cli
```

## 📊 API Documentation

### Core Endpoints

#### Knowledge Graph
- `GET /api/graph` - Retrieve graph data
- `POST /api/graph/extract` - Extract knowledge from text
- `PUT /api/graph/expand` - Expand graph with new connections
- `DELETE /api/graph/cleanup` - Clean up graph data

#### RAG System
- `POST /api/rag/query` - Query knowledge base
- `POST /api/rag/embed` - Create embeddings
- `GET /api/rag/similar` - Find similar content

#### AI Integration
- `POST /api/ai/analyze` - Analyze supplement information
- `POST /api/ai/extract` - Extract entities and relationships
- `GET /api/ai/models` - List available AI models

## 🧪 Testing

### Unit Tests
```bash
# Backend
cd backend && npm test

# Frontend
cd frontend && npm test
```

### Integration Tests
```bash
# Full system test
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

### E2E Tests
```bash
# Cypress tests
cd frontend && npm run test:e2e
```

## 🚀 Deployment

### Production Build
```bash
# Build all services
docker-compose -f docker-compose.prod.yml build

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

### Environment Variables

#### Backend (.env)
```env
NODE_ENV=development
PORT=3000
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password
WEAVIATE_URL=http://localhost:8080
REDIS_URL=redis://localhost:6379
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY=your_openai_api_key
```

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:3000
VITE_APP_NAME=Suplementor Knowledge Graph
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Based on technical analysis of knowledge graph visualization projects
- Inspired by robert-mcdermott/ai-knowledge-graph for LLM integration patterns
- Uses Neo4j LLM Graph Builder patterns for knowledge extraction
- Built with modern web technologies and best practices

## 📞 Support

For support, please open an issue in the GitHub repository or contact the development team.
