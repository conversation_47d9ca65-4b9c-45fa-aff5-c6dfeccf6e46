<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Suplementor Knowledge Graph</title>
    <meta name="description" content="Interactive knowledge graph for supplement information with AI-powered insights" />
    <meta name="keywords" content="supplements, knowledge graph, AI, health, nutrition, interactions" />
    <meta name="author" content="Suplementor Team" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Theme color -->
    <meta name="theme-color" content="#0ea5e9" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://suplementor.app/" />
    <meta property="og:title" content="Suplementor Knowledge Graph" />
    <meta property="og:description" content="Interactive knowledge graph for supplement information with AI-powered insights" />
    <meta property="og:image" content="/og-image.png" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://suplementor.app/" />
    <meta property="twitter:title" content="Suplementor Knowledge Graph" />
    <meta property="twitter:description" content="Interactive knowledge graph for supplement information with AI-powered insights" />
    <meta property="twitter:image" content="/twitter-image.png" />
    
    <!-- Prevent FOUC -->
    <style>
      html {
        visibility: hidden;
        opacity: 0;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Show content after fonts load -->
    <script>
      document.fonts.ready.then(() => {
        document.documentElement.style.visibility = 'visible';
        document.documentElement.style.opacity = '1';
      });
    </script>
  </body>
</html>
