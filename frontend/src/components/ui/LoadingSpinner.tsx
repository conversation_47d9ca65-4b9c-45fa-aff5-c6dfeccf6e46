import React from 'react';
import { motion } from 'framer-motion';
import { clsx } from 'clsx';

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  color?: 'blue' | 'purple' | 'green' | 'red' | 'gray';
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars' | 'medical';
  className?: string;
  text?: string;
}

const sizeVariants = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12'
};

const colorVariants = {
  blue: 'border-blue-500',
  purple: 'border-purple-500',
  green: 'border-green-500',
  red: 'border-red-500',
  gray: 'border-gray-500'
};

const textSizeVariants = {
  xs: 'text-xs',
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl'
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'blue',
  variant = 'spinner',
  className,
  text
}) => {
  const renderSpinner = () => {
    switch (variant) {
      case 'spinner':
        return (
          <motion.div
            className={clsx(
              'border-2 border-t-transparent rounded-full',
              sizeVariants[size],
              colorVariants[color]
            )}
            animate={{ rotate: 360 }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: 'linear'
            }}
          />
        );

      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className={clsx(
                  'rounded-full bg-current',
                  size === 'xs' ? 'w-1 h-1' :
                  size === 'sm' ? 'w-1.5 h-1.5' :
                  size === 'md' ? 'w-2 h-2' :
                  size === 'lg' ? 'w-3 h-3' : 'w-4 h-4',
                  color === 'blue' ? 'text-blue-500' :
                  color === 'purple' ? 'text-purple-500' :
                  color === 'green' ? 'text-green-500' :
                  color === 'red' ? 'text-red-500' : 'text-gray-500'
                )}
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: index * 0.2
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <motion.div
            className={clsx(
              'rounded-full',
              sizeVariants[size],
              color === 'blue' ? 'bg-blue-500' :
              color === 'purple' ? 'bg-purple-500' :
              color === 'green' ? 'bg-green-500' :
              color === 'red' ? 'bg-red-500' : 'bg-gray-500'
            )}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity
            }}
          />
        );

      case 'bars':
        return (
          <div className="flex space-x-1 items-end">
            {[0, 1, 2, 3].map((index) => (
              <motion.div
                key={index}
                className={clsx(
                  'rounded-sm',
                  size === 'xs' ? 'w-0.5' :
                  size === 'sm' ? 'w-1' :
                  size === 'md' ? 'w-1.5' :
                  size === 'lg' ? 'w-2' : 'w-3',
                  color === 'blue' ? 'bg-blue-500' :
                  color === 'purple' ? 'bg-purple-500' :
                  color === 'green' ? 'bg-green-500' :
                  color === 'red' ? 'bg-red-500' : 'bg-gray-500'
                )}
                animate={{
                  height: [
                    size === 'xs' ? '4px' :
                    size === 'sm' ? '6px' :
                    size === 'md' ? '8px' :
                    size === 'lg' ? '12px' : '16px',
                    
                    size === 'xs' ? '12px' :
                    size === 'sm' ? '16px' :
                    size === 'md' ? '24px' :
                    size === 'lg' ? '32px' : '48px',
                    
                    size === 'xs' ? '4px' :
                    size === 'sm' ? '6px' :
                    size === 'md' ? '8px' :
                    size === 'lg' ? '12px' : '16px'
                  ]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: index * 0.1
                }}
              />
            ))}
          </div>
        );

      case 'medical':
        return (
          <div className="relative">
            {/* DNA Helix Animation */}
            <motion.div
              className={clsx(
                'relative',
                sizeVariants[size]
              )}
            >
              {/* Outer ring */}
              <motion.div
                className={clsx(
                  'absolute inset-0 border-2 border-t-transparent rounded-full',
                  colorVariants[color]
                )}
                animate={{ rotate: 360 }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'linear'
                }}
              />
              
              {/* Inner ring */}
              <motion.div
                className={clsx(
                  'absolute inset-1 border-2 border-b-transparent rounded-full',
                  color === 'blue' ? 'border-blue-300' :
                  color === 'purple' ? 'border-purple-300' :
                  color === 'green' ? 'border-green-300' :
                  color === 'red' ? 'border-red-300' : 'border-gray-300'
                )}
                animate={{ rotate: -360 }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: 'linear'
                }}
              />
              
              {/* Center dot */}
              <motion.div
                className={clsx(
                  'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full',
                  size === 'xs' ? 'w-1 h-1' :
                  size === 'sm' ? 'w-1.5 h-1.5' :
                  size === 'md' ? 'w-2 h-2' :
                  size === 'lg' ? 'w-3 h-3' : 'w-4 h-4',
                  color === 'blue' ? 'bg-blue-500' :
                  color === 'purple' ? 'bg-purple-500' :
                  color === 'green' ? 'bg-green-500' :
                  color === 'red' ? 'bg-red-500' : 'bg-gray-500'
                )}
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.8, 1, 0.8]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity
                }}
              />
            </motion.div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={clsx('flex flex-col items-center justify-center', className)}>
      {renderSpinner()}
      {text && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className={clsx(
            'mt-2 text-gray-600 font-medium',
            textSizeVariants[size]
          )}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
};

// Specialized loading components
export const MedicalLoader: React.FC<Omit<LoadingSpinnerProps, 'variant'>> = (props) => (
  <LoadingSpinner variant="medical" {...props} />
);

export const DotsLoader: React.FC<Omit<LoadingSpinnerProps, 'variant'>> = (props) => (
  <LoadingSpinner variant="dots" {...props} />
);

export const BarsLoader: React.FC<Omit<LoadingSpinnerProps, 'variant'>> = (props) => (
  <LoadingSpinner variant="bars" {...props} />
);

// Loading overlay component
export const LoadingOverlay: React.FC<{
  isVisible: boolean;
  text?: string;
  variant?: LoadingSpinnerProps['variant'];
  className?: string;
}> = ({ isVisible, text, variant = 'medical', className }) => {
  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={clsx(
        'fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50',
        className
      )}
    >
      <div className="text-center">
        <LoadingSpinner
          variant={variant}
          size="xl"
          color="blue"
          text={text}
        />
      </div>
    </motion.div>
  );
};

// Inline loading component
export const InlineLoader: React.FC<{
  text?: string;
  className?: string;
}> = ({ text = 'Loading...', className }) => (
  <div className={clsx('flex items-center gap-2', className)}>
    <LoadingSpinner size="sm" variant="spinner" color="blue" />
    <span className="text-sm text-gray-600">{text}</span>
  </div>
);

export default LoadingSpinner;
