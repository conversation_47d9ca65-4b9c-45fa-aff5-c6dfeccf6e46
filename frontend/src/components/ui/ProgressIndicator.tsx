import React from 'react';
import { motion } from 'framer-motion';
import { clsx } from 'clsx';
import { CheckCircle, Clock, AlertCircle } from 'lucide-react';

interface ProgressIndicatorProps {
  progress: number; // 0-100
  variant?: 'linear' | 'circular' | 'steps' | 'medical';
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'purple' | 'red';
  showPercentage?: boolean;
  showLabel?: boolean;
  label?: string;
  className?: string;
  animated?: boolean;
}

interface StepProgressProps {
  steps: Array<{
    label: string;
    status: 'completed' | 'current' | 'pending';
    description?: string;
  }>;
  className?: string;
}

const sizeVariants = {
  sm: {
    height: 'h-2',
    text: 'text-sm',
    circular: 'w-12 h-12',
    strokeWidth: 2
  },
  md: {
    height: 'h-3',
    text: 'text-base',
    circular: 'w-16 h-16',
    strokeWidth: 3
  },
  lg: {
    height: 'h-4',
    text: 'text-lg',
    circular: 'w-20 h-20',
    strokeWidth: 4
  }
};

const colorVariants = {
  blue: {
    bg: 'bg-blue-500',
    text: 'text-blue-600',
    stroke: 'stroke-blue-500'
  },
  green: {
    bg: 'bg-green-500',
    text: 'text-green-600',
    stroke: 'stroke-green-500'
  },
  purple: {
    bg: 'bg-purple-500',
    text: 'text-purple-600',
    stroke: 'stroke-purple-500'
  },
  red: {
    bg: 'bg-red-500',
    text: 'text-red-600',
    stroke: 'stroke-red-500'
  }
};

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  variant = 'linear',
  size = 'md',
  color = 'blue',
  showPercentage = true,
  showLabel = false,
  label,
  className,
  animated = true
}) => {
  const clampedProgress = Math.max(0, Math.min(100, progress));

  const renderLinearProgress = () => (
    <div className={clsx('w-full', className)}>
      {(showLabel && label) && (
        <div className="flex justify-between items-center mb-2">
          <span className={clsx('font-medium', sizeVariants[size].text, colorVariants[color].text)}>
            {label}
          </span>
          {showPercentage && (
            <span className={clsx('font-medium', sizeVariants[size].text, colorVariants[color].text)}>
              {Math.round(clampedProgress)}%
            </span>
          )}
        </div>
      )}
      
      <div className={clsx(
        'w-full bg-gray-200 rounded-full overflow-hidden',
        sizeVariants[size].height
      )}>
        <motion.div
          className={clsx(
            'h-full rounded-full',
            colorVariants[color].bg
          )}
          initial={{ width: 0 }}
          animate={{ width: `${clampedProgress}%` }}
          transition={{
            duration: animated ? 0.8 : 0,
            ease: 'easeOut'
          }}
        />
      </div>
      
      {showPercentage && !showLabel && (
        <div className="text-center mt-2">
          <span className={clsx('font-medium', sizeVariants[size].text, colorVariants[color].text)}>
            {Math.round(clampedProgress)}%
          </span>
        </div>
      )}
    </div>
  );

  const renderCircularProgress = () => {
    const radius = size === 'sm' ? 20 : size === 'md' ? 28 : 36;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (clampedProgress / 100) * circumference;

    return (
      <div className={clsx('relative', className)}>
        <svg
          className={clsx(sizeVariants[size].circular, 'transform -rotate-90')}
          viewBox="0 0 100 100"
        >
          {/* Background circle */}
          <circle
            cx="50"
            cy="50"
            r={radius}
            fill="none"
            stroke="currentColor"
            strokeWidth={sizeVariants[size].strokeWidth}
            className="text-gray-200"
          />
          
          {/* Progress circle */}
          <motion.circle
            cx="50"
            cy="50"
            r={radius}
            fill="none"
            strokeWidth={sizeVariants[size].strokeWidth}
            strokeLinecap="round"
            className={colorVariants[color].stroke}
            initial={{ strokeDasharray: circumference, strokeDashoffset: circumference }}
            animate={{ strokeDashoffset }}
            transition={{
              duration: animated ? 1 : 0,
              ease: 'easeOut'
            }}
            style={{
              strokeDasharray: circumference,
            }}
          />
        </svg>
        
        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className={clsx(
            'font-bold',
            sizeVariants[size].text,
            colorVariants[color].text
          )}>
            {Math.round(clampedProgress)}%
          </span>
        </div>
        
        {showLabel && label && (
          <div className="text-center mt-2">
            <span className={clsx('font-medium', sizeVariants[size].text)}>
              {label}
            </span>
          </div>
        )}
      </div>
    );
  };

  const renderMedicalProgress = () => (
    <div className={clsx('w-full', className)}>
      {(showLabel && label) && (
        <div className="flex justify-between items-center mb-3">
          <span className={clsx('font-medium', sizeVariants[size].text)}>
            {label}
          </span>
          {showPercentage && (
            <span className={clsx('font-bold', sizeVariants[size].text, colorVariants[color].text)}>
              {Math.round(clampedProgress)}%
            </span>
          )}
        </div>
      )}
      
      <div className="relative">
        {/* Background */}
        <div className={clsx(
          'w-full bg-gray-100 rounded-full overflow-hidden border',
          sizeVariants[size].height
        )}>
          {/* Progress bar with gradient */}
          <motion.div
            className={clsx(
              'h-full rounded-full bg-gradient-to-r',
              color === 'blue' ? 'from-blue-400 to-blue-600' :
              color === 'green' ? 'from-green-400 to-green-600' :
              color === 'purple' ? 'from-purple-400 to-purple-600' :
              'from-red-400 to-red-600'
            )}
            initial={{ width: 0 }}
            animate={{ width: `${clampedProgress}%` }}
            transition={{
              duration: animated ? 1.2 : 0,
              ease: 'easeOut'
            }}
          />
        </div>
        
        {/* Pulse effect */}
        {animated && clampedProgress > 0 && (
          <motion.div
            className={clsx(
              'absolute top-0 left-0 h-full rounded-full opacity-30',
              colorVariants[color].bg
            )}
            animate={{
              width: [`${clampedProgress}%`, `${Math.min(clampedProgress + 10, 100)}%`, `${clampedProgress}%`]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
        )}
      </div>
      
      {/* Status indicators */}
      <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
        <span>Processing...</span>
        <span>{Math.round(clampedProgress)}/100</span>
      </div>
    </div>
  );

  switch (variant) {
    case 'circular':
      return renderCircularProgress();
    case 'medical':
      return renderMedicalProgress();
    case 'linear':
    default:
      return renderLinearProgress();
  }
};

// Step Progress Component
export const StepProgress: React.FC<StepProgressProps> = ({ steps, className }) => {
  return (
    <div className={clsx('w-full', className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={index} className="flex flex-col items-center flex-1">
            {/* Step indicator */}
            <div className="relative flex items-center">
              {/* Line to previous step */}
              {index > 0 && (
                <div className={clsx(
                  'absolute right-full w-full h-0.5 -translate-x-2',
                  steps[index - 1].status === 'completed' ? 'bg-green-500' : 'bg-gray-300'
                )} />
              )}
              
              {/* Step circle */}
              <motion.div
                className={clsx(
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border-2 z-10',
                  step.status === 'completed' ? 'bg-green-500 border-green-500 text-white' :
                  step.status === 'current' ? 'bg-blue-500 border-blue-500 text-white' :
                  'bg-white border-gray-300 text-gray-500'
                )}
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                {step.status === 'completed' ? (
                  <CheckCircle className="w-4 h-4" />
                ) : step.status === 'current' ? (
                  <Clock className="w-4 h-4" />
                ) : (
                  <span>{index + 1}</span>
                )}
              </motion.div>
              
              {/* Line to next step */}
              {index < steps.length - 1 && (
                <div className={clsx(
                  'absolute left-full w-full h-0.5 translate-x-2',
                  step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300'
                )} />
              )}
            </div>
            
            {/* Step label */}
            <div className="mt-2 text-center">
              <div className={clsx(
                'text-sm font-medium',
                step.status === 'completed' ? 'text-green-600' :
                step.status === 'current' ? 'text-blue-600' :
                'text-gray-500'
              )}>
                {step.label}
              </div>
              {step.description && (
                <div className="text-xs text-gray-400 mt-1">
                  {step.description}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Specialized progress components
export const MedicalProgress: React.FC<Omit<ProgressIndicatorProps, 'variant'>> = (props) => (
  <ProgressIndicator variant="medical" {...props} />
);

export const CircularProgress: React.FC<Omit<ProgressIndicatorProps, 'variant'>> = (props) => (
  <ProgressIndicator variant="circular" {...props} />
);

// Research progress component
export const ResearchProgress: React.FC<{
  currentStep: 'searching' | 'crawling' | 'analyzing' | 'completed';
  progress: number;
  className?: string;
}> = ({ currentStep, progress, className }) => {
  const steps = [
    {
      label: 'Searching',
      status: currentStep === 'searching' ? 'current' as const :
              ['crawling', 'analyzing', 'completed'].includes(currentStep) ? 'completed' as const : 'pending' as const,
      description: 'Web search & data collection'
    },
    {
      label: 'Crawling',
      status: currentStep === 'crawling' ? 'current' as const :
              ['analyzing', 'completed'].includes(currentStep) ? 'completed' as const : 'pending' as const,
      description: 'Content extraction'
    },
    {
      label: 'Analyzing',
      status: currentStep === 'analyzing' ? 'current' as const :
              currentStep === 'completed' ? 'completed' as const : 'pending' as const,
      description: 'AI medical analysis'
    }
  ];

  return (
    <div className={clsx('space-y-4', className)}>
      <StepProgress steps={steps} />
      <MedicalProgress
        progress={progress}
        color="blue"
        showPercentage
        label="Overall Progress"
        animated
      />
    </div>
  );
};

export default ProgressIndicator;
