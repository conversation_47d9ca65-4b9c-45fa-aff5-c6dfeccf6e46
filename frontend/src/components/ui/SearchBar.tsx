import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Clock, TrendingUp, Sparkles } from 'lucide-react';
import { clsx } from 'clsx';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: () => void;
  placeholder?: string;
  suggestions?: string[];
  recentSearches?: string[];
  isLoading?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'elevated' | 'glass';
  autoFocus?: boolean;
  showSuggestions?: boolean;
}

const sizeVariants = {
  sm: 'h-10 text-sm',
  md: 'h-12 text-base',
  lg: 'h-14 text-lg'
};

const variantStyles = {
  default: 'bg-white border border-gray-300 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20',
  elevated: 'bg-white shadow-lg border-0 focus-within:shadow-xl',
  glass: 'bg-white/80 backdrop-blur-sm border border-white/20 focus-within:bg-white/90'
};

export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  onSearch,
  placeholder = 'Search...',
  suggestions = [],
  recentSearches = [],
  isLoading = false,
  className,
  size = 'md',
  variant = 'default',
  autoFocus = false,
  showSuggestions = true
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Sample suggestions for supplements
  const defaultSuggestions = [
    'Vitamin D3 benefits',
    'Omega-3 fish oil',
    'Magnesium glycinate',
    'Probiotics gut health',
    'Curcumin turmeric',
    'Ashwagandha stress',
    'Zinc immune system',
    'B12 vitamin deficiency'
  ];

  const activeSuggestions = suggestions.length > 0 ? suggestions : defaultSuggestions;
  const filteredSuggestions = activeSuggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(value.toLowerCase()) && suggestion !== value
  );

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setShowDropdown(newValue.length > 0 && showSuggestions);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onSearch();
      setShowDropdown(false);
    } else if (e.key === 'Escape') {
      setShowDropdown(false);
      inputRef.current?.blur();
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    setShowDropdown(false);
    onSearch();
  };

  const clearSearch = () => {
    onChange('');
    inputRef.current?.focus();
    setShowDropdown(false);
  };

  return (
    <div className={clsx('relative w-full', className)}>
      {/* Main Search Input */}
      <motion.div
        className={clsx(
          'relative flex items-center rounded-xl transition-all duration-200',
          sizeVariants[size],
          variantStyles[variant],
          isFocused && 'ring-2 ring-blue-500/20'
        )}
        whileHover={{ scale: 1.01 }}
        whileFocus={{ scale: 1.02 }}
      >
        {/* Search Icon */}
        <div className="flex items-center justify-center w-12 h-full">
          <motion.div
            animate={{
              rotate: isLoading ? 360 : 0,
              scale: isFocused ? 1.1 : 1
            }}
            transition={{
              rotate: { duration: 1, repeat: isLoading ? Infinity : 0, ease: 'linear' },
              scale: { duration: 0.2 }
            }}
          >
            <Search className={clsx(
              'transition-colors duration-200',
              isFocused ? 'text-blue-500' : 'text-gray-400',
              size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'
            )} />
          </motion.div>
        </div>

        {/* Input Field */}
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            setIsFocused(true);
            if (value.length > 0 && showSuggestions) {
              setShowDropdown(true);
            }
          }}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          className="flex-1 h-full bg-transparent border-0 outline-none placeholder-gray-400 text-gray-900"
        />

        {/* Clear Button */}
        <AnimatePresence>
          {value && (
            <motion.button
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              onClick={clearSearch}
              className="flex items-center justify-center w-8 h-8 mr-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-4 h-4" />
            </motion.button>
          )}
        </AnimatePresence>

        {/* Search Button */}
        <motion.button
          onClick={onSearch}
          disabled={isLoading}
          className={clsx(
            'flex items-center justify-center px-4 h-full bg-blue-500 text-white rounded-r-xl font-medium transition-colors disabled:opacity-50',
            'hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500/20'
          )}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isLoading ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
            />
          ) : (
            <span className={size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base'}>
              Search
            </span>
          )}
        </motion.button>
      </motion.div>

      {/* Suggestions Dropdown */}
      <AnimatePresence>
        {showDropdown && (filteredSuggestions.length > 0 || recentSearches.length > 0) && (
          <motion.div
            ref={dropdownRef}
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto"
          >
            {/* Recent Searches */}
            {recentSearches.length > 0 && value.length === 0 && (
              <div className="p-3 border-b border-gray-100">
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                  <Clock className="w-4 h-4" />
                  Recent Searches
                </div>
                {recentSearches.slice(0, 3).map((search, index) => (
                  <motion.button
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    onClick={() => handleSuggestionClick(search)}
                    className="w-full text-left px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    {search}
                  </motion.button>
                ))}
              </div>
            )}

            {/* Suggestions */}
            {filteredSuggestions.length > 0 && (
              <div className="p-3">
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                  <Sparkles className="w-4 h-4" />
                  Suggestions
                </div>
                {filteredSuggestions.slice(0, 6).map((suggestion, index) => (
                  <motion.button
                    key={suggestion}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-lg transition-colors flex items-center justify-between group"
                  >
                    <span>{suggestion}</span>
                    <TrendingUp className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </motion.button>
                ))}
              </div>
            )}

            {/* No Results */}
            {filteredSuggestions.length === 0 && recentSearches.length === 0 && value.length > 0 && (
              <div className="p-6 text-center text-gray-500">
                <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No suggestions found</p>
                <p className="text-sm">Press Enter to search for "{value}"</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Compact search bar variant
export const CompactSearchBar: React.FC<Omit<SearchBarProps, 'size'>> = (props) => (
  <SearchBar size="sm" {...props} />
);

// Large search bar variant
export const HeroSearchBar: React.FC<Omit<SearchBarProps, 'size' | 'variant'>> = (props) => (
  <SearchBar size="lg" variant="elevated" {...props} />
);

// Glass search bar variant
export const GlassSearchBar: React.FC<Omit<SearchBarProps, 'variant'>> = (props) => (
  <SearchBar variant="glass" {...props} />
);

export default SearchBar;
