import React, { ReactNode } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { clsx } from 'clsx';

interface AnimatedCardProps extends Omit<HTMLMotionProps<'div'>, 'children'> {
  children: ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'glass' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  glow?: boolean;
  className?: string;
}

const cardVariants = {
  default: 'bg-white border border-gray-200 shadow-sm',
  elevated: 'bg-white shadow-lg border-0',
  outlined: 'bg-transparent border-2 border-gray-300',
  glass: 'bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg',
  gradient: 'bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200 shadow-sm'
};

const sizeVariants = {
  sm: 'rounded-lg',
  md: 'rounded-xl',
  lg: 'rounded-2xl',
  xl: 'rounded-3xl'
};

const hoverVariants = {
  hover: {
    y: -4,
    scale: 1.02,
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 20
    }
  },
  tap: {
    scale: 0.98,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 25
    }
  }
};

const glowVariants = {
  initial: {
    boxShadow: '0 0 0 rgba(59, 130, 246, 0)'
  },
  hover: {
    boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)',
    transition: {
      duration: 0.3
    }
  }
};

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  variant = 'default',
  size = 'md',
  hover = true,
  glow = false,
  className,
  ...motionProps
}) => {
  const cardClasses = clsx(
    'transition-all duration-200',
    cardVariants[variant],
    sizeVariants[size],
    glow && 'relative',
    className
  );

  const motionVariants = {
    initial: {
      opacity: 0,
      y: 20,
      scale: 0.95,
      ...(glow && glowVariants.initial)
    },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
        duration: 0.6
      }
    },
    ...(hover && hoverVariants),
    ...(glow && {
      whileHover: {
        ...hoverVariants.hover,
        ...glowVariants.hover
      }
    })
  };

  return (
    <motion.div
      className={cardClasses}
      variants={motionVariants}
      initial="initial"
      animate="animate"
      whileHover={hover ? "hover" : undefined}
      whileTap={hover ? "tap" : undefined}
      {...motionProps}
    >
      {glow && (
        <div className="absolute inset-0 rounded-inherit bg-gradient-to-r from-blue-500/20 to-purple-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
      )}
      {children}
    </motion.div>
  );
};

// Specialized card variants
export const GlassCard: React.FC<Omit<AnimatedCardProps, 'variant'>> = (props) => (
  <AnimatedCard variant="glass" {...props} />
);

export const GradientCard: React.FC<Omit<AnimatedCardProps, 'variant'>> = (props) => (
  <AnimatedCard variant="gradient" {...props} />
);

export const ElevatedCard: React.FC<Omit<AnimatedCardProps, 'variant'>> = (props) => (
  <AnimatedCard variant="elevated" {...props} />
);

// Interactive card with click animation
export const InteractiveCard: React.FC<AnimatedCardProps & { onClick?: () => void }> = ({
  onClick,
  children,
  ...props
}) => {
  return (
    <AnimatedCard
      {...props}
      onClick={onClick}
      className={clsx(
        onClick && 'cursor-pointer select-none',
        props.className
      )}
      whileTap={onClick ? { scale: 0.95 } : undefined}
    >
      {children}
    </AnimatedCard>
  );
};

// Card with loading state
export const LoadingCard: React.FC<{
  isLoading: boolean;
  children: ReactNode;
  loadingContent?: ReactNode;
  className?: string;
}> = ({ isLoading, children, loadingContent, className }) => {
  return (
    <AnimatedCard className={className}>
      <motion.div
        animate={{
          opacity: isLoading ? 0.6 : 1,
          filter: isLoading ? 'blur(2px)' : 'blur(0px)'
        }}
        transition={{ duration: 0.3 }}
      >
        {children}
      </motion.div>
      
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-inherit"
        >
          {loadingContent || (
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              <span className="text-gray-600 font-medium">Loading...</span>
            </div>
          )}
        </motion.div>
      )}
    </AnimatedCard>
  );
};

// Card with reveal animation
export const RevealCard: React.FC<AnimatedCardProps & {
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}> = ({ delay = 0, direction = 'up', children, ...props }) => {
  const directionVariants = {
    up: { y: 50 },
    down: { y: -50 },
    left: { x: 50 },
    right: { x: -50 }
  };

  return (
    <motion.div
      initial={{
        opacity: 0,
        ...directionVariants[direction]
      }}
      animate={{
        opacity: 1,
        x: 0,
        y: 0
      }}
      transition={{
        delay,
        type: 'spring',
        stiffness: 300,
        damping: 30
      }}
    >
      <AnimatedCard {...props}>
        {children}
      </AnimatedCard>
    </motion.div>
  );
};

// Card with flip animation
export const FlipCard: React.FC<{
  front: ReactNode;
  back: ReactNode;
  isFlipped: boolean;
  className?: string;
}> = ({ front, back, isFlipped, className }) => {
  return (
    <div className={clsx('relative w-full h-full perspective-1000', className)}>
      <motion.div
        className="relative w-full h-full preserve-3d"
        animate={{ rotateY: isFlipped ? 180 : 0 }}
        transition={{ duration: 0.6, type: 'spring', stiffness: 300, damping: 30 }}
      >
        {/* Front */}
        <div className="absolute inset-0 backface-hidden">
          <AnimatedCard className="w-full h-full">
            {front}
          </AnimatedCard>
        </div>
        
        {/* Back */}
        <div className="absolute inset-0 backface-hidden rotate-y-180">
          <AnimatedCard className="w-full h-full">
            {back}
          </AnimatedCard>
        </div>
      </motion.div>
    </div>
  );
};

// Card with expand animation
export const ExpandableCard: React.FC<{
  title: ReactNode;
  children: ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
  className?: string;
}> = ({ title, children, isExpanded, onToggle, className }) => {
  return (
    <AnimatedCard className={className}>
      <motion.div
        className="cursor-pointer"
        onClick={onToggle}
        whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}
        transition={{ duration: 0.2 }}
      >
        <div className="p-4 flex items-center justify-between">
          {title}
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </motion.div>
        </div>
      </motion.div>
      
      <motion.div
        initial={false}
        animate={{
          height: isExpanded ? 'auto' : 0,
          opacity: isExpanded ? 1 : 0
        }}
        transition={{
          height: { duration: 0.3, ease: 'easeInOut' },
          opacity: { duration: 0.2, delay: isExpanded ? 0.1 : 0 }
        }}
        className="overflow-hidden"
      >
        <div className="p-4 pt-0 border-t border-gray-100">
          {children}
        </div>
      </motion.div>
    </AnimatedCard>
  );
};

export default AnimatedCard;
