import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { motion } from 'framer-motion';
import { ZoomIn, ZoomOut, RotateCcw, Maximize2, Settings } from 'lucide-react';

import { GraphData, GraphNode, GraphRelationship, GraphEvent } from '@/types';
import Button from '@/components/atoms/Button';
import Card from '@/components/atoms/Card';
import Badge from '@/components/atoms/Badge';

interface GraphVisualizationProps {
  data: GraphData;
  width?: number;
  height?: number;
  onNodeClick?: (node: GraphNode, event: MouseEvent) => void;
  onNodeHover?: (node: GraphNode | null, event: MouseEvent) => void;
  onRelationshipClick?: (relationship: GraphRelationship, event: MouseEvent) => void;
  onBackgroundClick?: (event: MouseEvent) => void;
  selectedNodeIds?: Set<string>;
  highlightedNodeIds?: Set<string>;
  showLabels?: boolean;
  showRelationshipLabels?: boolean;
  nodeSize?: number;
  linkDistance?: number;
  chargeStrength?: number;
  className?: string;
}

// Node type colors
const nodeColors = {
  Supplement: '#3b82f6', // blue
  Ingredient: '#10b981', // emerald
  Effect: '#f59e0b', // amber
  Study: '#8b5cf6', // violet
  Condition: '#ef4444', // red
  Interaction: '#f97316', // orange
  default: '#6b7280', // gray
};

// Relationship type colors
const linkColors = {
  CONTAINS: '#10b981',
  CAUSES: '#f59e0b',
  INTERACTS_WITH: '#ef4444',
  STUDIED_IN: '#8b5cf6',
  TREATS: '#3b82f6',
  CONTRAINDICATED_WITH: '#dc2626',
  default: '#9ca3af',
};

const GraphVisualization: React.FC<GraphVisualizationProps> = ({
  data,
  width = 800,
  height = 600,
  onNodeClick,
  onNodeHover,
  onRelationshipClick,
  onBackgroundClick,
  selectedNodeIds = new Set(),
  highlightedNodeIds = new Set(),
  showLabels = true,
  showRelationshipLabels = false,
  nodeSize = 8,
  linkDistance = 100,
  chargeStrength = -300,
  className,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const simulationRef = useRef<d3.Simulation<GraphNode, GraphRelationship> | null>(null);
  
  const [zoom, setZoom] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null);
  const [tooltip, setTooltip] = useState<{ x: number; y: number; content: string } | null>(null);

  // Initialize D3 simulation
  const initializeSimulation = useCallback(() => {
    if (!svgRef.current || !data.nodes.length) return;

    const svg = d3.select(svgRef.current);
    const container = svg.select('.graph-container');
    
    // Clear previous content
    container.selectAll('*').remove();

    // Create groups for links and nodes
    const linkGroup = container.append('g').attr('class', 'links');
    const nodeGroup = container.append('g').attr('class', 'nodes');
    const labelGroup = container.append('g').attr('class', 'labels');

    // Create force simulation
    const simulation = d3.forceSimulation<GraphNode>(data.nodes)
      .force('link', d3.forceLink<GraphNode, GraphRelationship>(data.relationships)
        .id(d => d.id)
        .distance(linkDistance)
      )
      .force('charge', d3.forceManyBody().strength(chargeStrength))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(nodeSize + 2));

    simulationRef.current = simulation;

    // Create links
    const links = linkGroup
      .selectAll('line')
      .data(data.relationships)
      .enter()
      .append('line')
      .attr('class', 'graph-link')
      .attr('stroke', d => linkColors[d.type as keyof typeof linkColors] || linkColors.default)
      .attr('stroke-width', 2)
      .attr('stroke-opacity', 0.6)
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        event.stopPropagation();
        onRelationshipClick?.(d, event);
      })
      .on('mouseover', (event, d) => {
        setTooltip({
          x: event.pageX,
          y: event.pageY,
          content: `${d.type}: ${d.properties.description || 'No description'}`,
        });
      })
      .on('mouseout', () => {
        setTooltip(null);
      });

    // Create nodes
    const nodes = nodeGroup
      .selectAll('circle')
      .data(data.nodes)
      .enter()
      .append('circle')
      .attr('class', 'graph-node')
      .attr('r', nodeSize)
      .attr('fill', d => {
        const nodeType = d.labels[0] || 'default';
        return nodeColors[nodeType as keyof typeof nodeColors] || nodeColors.default;
      })
      .attr('stroke', d => selectedNodeIds.has(d.id) ? '#1f2937' : '#fff')
      .attr('stroke-width', d => selectedNodeIds.has(d.id) ? 3 : 2)
      .style('cursor', 'pointer')
      .call(d3.drag<SVGCircleElement, GraphNode>()
        .on('start', (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        })
        .on('drag', (event, d) => {
          d.fx = event.x;
          d.fy = event.y;
        })
        .on('end', (event, d) => {
          if (!event.active) simulation.alphaTarget(0);
          d.fx = null;
          d.fy = null;
        })
      )
      .on('click', (event, d) => {
        event.stopPropagation();
        onNodeClick?.(d, event);
      })
      .on('mouseover', (event, d) => {
        setHoveredNode(d);
        onNodeHover?.(d, event);
        setTooltip({
          x: event.pageX,
          y: event.pageY,
          content: `${d.properties.name}\n${d.properties.description || ''}`,
        });
      })
      .on('mouseout', (event, d) => {
        setHoveredNode(null);
        onNodeHover?.(null, event);
        setTooltip(null);
      });

    // Create node labels
    const labels = labelGroup
      .selectAll('text')
      .data(data.nodes)
      .enter()
      .append('text')
      .attr('class', 'node-label')
      .attr('text-anchor', 'middle')
      .attr('dy', nodeSize + 15)
      .attr('font-size', '12px')
      .attr('font-family', 'Inter, sans-serif')
      .attr('fill', '#374151')
      .style('pointer-events', 'none')
      .style('opacity', showLabels ? 1 : 0)
      .text(d => d.properties.name);

    // Create relationship labels
    if (showRelationshipLabels) {
      const relationshipLabels = labelGroup
        .selectAll('.relationship-label')
        .data(data.relationships)
        .enter()
        .append('text')
        .attr('class', 'relationship-label')
        .attr('text-anchor', 'middle')
        .attr('font-size', '10px')
        .attr('font-family', 'Inter, sans-serif')
        .attr('fill', '#6b7280')
        .style('pointer-events', 'none')
        .text(d => d.type);
    }

    // Update positions on simulation tick
    simulation.on('tick', () => {
      links
        .attr('x1', d => (d.source as GraphNode).x!)
        .attr('y1', d => (d.source as GraphNode).y!)
        .attr('x2', d => (d.target as GraphNode).x!)
        .attr('y2', d => (d.target as GraphNode).y!);

      nodes
        .attr('cx', d => d.x!)
        .attr('cy', d => d.y!);

      labels
        .attr('x', d => d.x!)
        .attr('y', d => d.y!);

      if (showRelationshipLabels) {
        labelGroup.selectAll('.relationship-label')
          .attr('x', d => ((d.source as GraphNode).x! + (d.target as GraphNode).x!) / 2)
          .attr('y', d => ((d.source as GraphNode).y! + (d.target as GraphNode).y!) / 2);
      }
    });

    // Setup zoom behavior
    const zoomBehavior = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 10])
      .on('zoom', (event) => {
        container.attr('transform', event.transform);
        setZoom(event.transform.k);
      });

    svg.call(zoomBehavior);

    // Background click handler
    svg.on('click', (event) => {
      if (event.target === svg.node()) {
        onBackgroundClick?.(event);
      }
    });

  }, [data, width, height, nodeSize, linkDistance, chargeStrength, showLabels, showRelationshipLabels, selectedNodeIds, onNodeClick, onNodeHover, onRelationshipClick, onBackgroundClick]);

  // Initialize simulation when data changes
  useEffect(() => {
    initializeSimulation();
    
    return () => {
      if (simulationRef.current) {
        simulationRef.current.stop();
      }
    };
  }, [initializeSimulation]);

  // Zoom controls
  const handleZoomIn = () => {
    const svg = d3.select(svgRef.current);
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
      1.5
    );
  };

  const handleZoomOut = () => {
    const svg = d3.select(svgRef.current);
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
      1 / 1.5
    );
  };

  const handleResetZoom = () => {
    const svg = d3.select(svgRef.current);
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().transform as any,
      d3.zoomIdentity
    );
  };

  const handleFullscreen = () => {
    if (!isFullscreen) {
      containerRef.current?.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div ref={containerRef} className={`graph-container ${className}`}>
      {/* Graph Controls */}
      <div className="absolute top-4 right-4 z-10 flex flex-col gap-2">
        <Card padding="sm" className="flex flex-col gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={handleZoomIn}
            title="Zoom In"
          >
            <ZoomIn className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleZoomOut}
            title="Zoom Out"
          >
            <ZoomOut className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleResetZoom}
            title="Reset Zoom"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleFullscreen}
            title="Fullscreen"
          >
            <Maximize2 className="w-4 h-4" />
          </Button>
        </Card>
      </div>

      {/* Graph Stats */}
      <div className="absolute top-4 left-4 z-10">
        <Card padding="sm">
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              <span>{data.nodes.length} nodes</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-1 bg-gray-400"></div>
              <span>{data.relationships.length} links</span>
            </div>
            <Badge variant="secondary" size="sm">
              Zoom: {Math.round(zoom * 100)}%
            </Badge>
          </div>
        </Card>
      </div>

      {/* Main SVG */}
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="graph-svg"
      >
        <defs>
          {/* Arrow markers for directed relationships */}
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              fill="#9ca3af"
            />
          </marker>
        </defs>
        <g className="graph-container"></g>
      </svg>

      {/* Tooltip */}
      {tooltip && (
        <motion.div
          className="graph-tooltip"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 10,
          }}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
        >
          {tooltip.content.split('\n').map((line, index) => (
            <div key={index}>{line}</div>
          ))}
        </motion.div>
      )}

      {/* Legend */}
      <div className="absolute bottom-4 left-4 z-10">
        <Card padding="sm">
          <div className="text-xs font-medium text-gray-700 mb-2">Node Types</div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {Object.entries(nodeColors).map(([type, color]) => (
              type !== 'default' && (
                <div key={type} className="flex items-center gap-1">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: color }}
                  ></div>
                  <span className="text-gray-600">{type}</span>
                </div>
              )
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default GraphVisualization;
