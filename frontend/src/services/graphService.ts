import { apiClient } from './api';
import { 
  GraphData, 
  GraphNode, 
  GraphRelationship, 
  GraphFilters, 
  GraphStats,
  PaginatedResponse 
} from '@/types';

export interface NodeCreationData {
  type: string;
  properties: {
    name: string;
    description?: string;
    [key: string]: any;
  };
}

export interface RelationshipCreationData {
  fromId: string;
  toId: string;
  type: string;
  properties?: {
    [key: string]: any;
  };
}

export interface RelationshipFilters {
  direction?: 'incoming' | 'outgoing' | 'both';
  types?: string[];
  limit?: number;
}

export interface SearchFilters {
  nodeTypes?: string[];
  limit?: number;
}

export interface ExtractionOptions {
  source?: string;
  extractionType?: 'supplement' | 'ingredient' | 'study' | 'general';
}

export interface ExpansionOptions {
  expansionType?: 'related' | 'similar' | 'interactions' | 'studies';
  limit?: number;
}

class GraphService {
  // Get graph data with filters
  async getGraphData(filters: GraphFilters = {}): Promise<GraphData> {
    return apiClient.get('/graph/data', { params: filters });
  }

  // Get a specific node by ID
  async getNodeById(id: string): Promise<GraphNode> {
    return apiClient.get(`/graph/nodes/${id}`);
  }

  // Get node relationships
  async getNodeRelationships(
    id: string, 
    filters: RelationshipFilters = {}
  ): Promise<GraphRelationship[]> {
    return apiClient.get(`/graph/nodes/${id}/relationships`, { params: filters });
  }

  // Search nodes
  async searchNodes(
    query: string, 
    filters: SearchFilters = {}
  ): Promise<GraphNode[]> {
    return apiClient.get('/graph/search', { 
      params: { q: query, ...filters } 
    });
  }

  // Create a new node
  async createNode(data: NodeCreationData): Promise<GraphNode> {
    return apiClient.post('/graph/nodes', data);
  }

  // Update a node
  async updateNode(id: string, properties: any): Promise<GraphNode> {
    return apiClient.patch(`/graph/nodes/${id}`, { properties });
  }

  // Delete a node
  async deleteNode(id: string): Promise<void> {
    return apiClient.delete(`/graph/nodes/${id}`);
  }

  // Create a relationship
  async createRelationship(data: RelationshipCreationData): Promise<GraphRelationship> {
    return apiClient.post('/graph/relationships', data);
  }

  // Delete a relationship
  async deleteRelationship(id: string): Promise<void> {
    return apiClient.delete(`/graph/relationships/${id}`);
  }

  // Extract knowledge from text
  async extractKnowledgeFromText(
    text: string, 
    options: ExtractionOptions = {}
  ): Promise<{
    extractedEntities: number;
    extractedRelationships: number;
    createdNodes: number;
    createdRelationships: number;
    nodes: GraphNode[];
    relationships: GraphRelationship[];
  }> {
    return apiClient.post('/graph/extract', { text, ...options });
  }

  // Expand graph around a node
  async expandGraph(
    nodeId: string, 
    options: ExpansionOptions = {}
  ): Promise<{
    sourceNode: GraphNode;
    expandedNodes: GraphNode[];
    expandedRelationships: GraphRelationship[];
    expansionType: string;
  }> {
    return apiClient.post(`/graph/nodes/${nodeId}/expand`, options);
  }

  // Get graph statistics
  async getGraphStats(): Promise<GraphStats> {
    return apiClient.get('/graph/stats');
  }

  // Cleanup graph (remove orphaned nodes, low-confidence relationships)
  async cleanupGraph(dryRun: boolean = true): Promise<{
    dryRun: boolean;
    cleanupPlan: {
      orphanedNodes: number;
      duplicateGroups: number;
      lowConfidenceRelationships: number;
      details: any;
    };
    executed: boolean;
  }> {
    return apiClient.post('/graph/cleanup', { dryRun });
  }

  // Get available node types
  async getNodeTypes(): Promise<string[]> {
    const stats = await this.getGraphStats();
    return stats.nodesByType.map(item => item.label);
  }

  // Get available relationship types
  async getRelationshipTypes(): Promise<string[]> {
    const stats = await this.getGraphStats();
    return stats.relationshipsByType.map(item => item.type);
  }

  // Batch operations
  async batchCreateNodes(nodes: NodeCreationData[]): Promise<GraphNode[]> {
    return apiClient.post('/graph/nodes/batch', { nodes });
  }

  async batchCreateRelationships(relationships: RelationshipCreationData[]): Promise<GraphRelationship[]> {
    return apiClient.post('/graph/relationships/batch', { relationships });
  }

  async batchDeleteNodes(nodeIds: string[]): Promise<void> {
    return apiClient.delete('/graph/nodes/batch', { data: { nodeIds } });
  }

  // Export/Import
  async exportGraph(format: 'json' | 'csv' | 'graphml' = 'json'): Promise<Blob> {
    const response = await fetch(`${apiClient.defaults.baseURL}/graph/export?format=${format}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
    });
    
    if (!response.ok) {
      throw new Error('Export failed');
    }
    
    return response.blob();
  }

  async importGraph(file: File): Promise<{
    importedNodes: number;
    importedRelationships: number;
    errors: string[];
  }> {
    const formData = new FormData();
    formData.append('file', file);
    
    return apiClient.upload('/graph/import', formData);
  }

  // Advanced queries
  async findShortestPath(
    fromNodeId: string, 
    toNodeId: string, 
    maxDepth: number = 5
  ): Promise<{
    path: GraphNode[];
    relationships: GraphRelationship[];
    distance: number;
  } | null> {
    return apiClient.get('/graph/path', {
      params: { from: fromNodeId, to: toNodeId, maxDepth }
    });
  }

  async findSimilarNodes(
    nodeId: string, 
    limit: number = 10
  ): Promise<Array<{
    node: GraphNode;
    similarity: number;
    commonNeighbors: number;
  }>> {
    return apiClient.get(`/graph/nodes/${nodeId}/similar`, {
      params: { limit }
    });
  }

  async getNodeNeighborhood(
    nodeId: string, 
    depth: number = 1
  ): Promise<GraphData> {
    return apiClient.get(`/graph/nodes/${nodeId}/neighborhood`, {
      params: { depth }
    });
  }

  // Graph algorithms
  async detectCommunities(): Promise<Array<{
    id: string;
    nodes: string[];
    size: number;
    modularity: number;
  }>> {
    return apiClient.get('/graph/communities');
  }

  async calculateCentrality(
    algorithm: 'betweenness' | 'closeness' | 'eigenvector' | 'pagerank' = 'pagerank'
  ): Promise<Array<{
    nodeId: string;
    score: number;
  }>> {
    return apiClient.get('/graph/centrality', {
      params: { algorithm }
    });
  }

  // Real-time updates (if WebSocket is implemented)
  subscribeToGraphUpdates(callback: (update: any) => void): () => void {
    // This would implement WebSocket connection for real-time updates
    // For now, return a no-op unsubscribe function
    return () => {};
  }
}

// Create and export service instance
export const graphService = new GraphService();
export default graphService;
