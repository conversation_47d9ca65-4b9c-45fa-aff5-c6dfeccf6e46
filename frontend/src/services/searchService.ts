import { apiClient } from './api';
import { SearchResult, SearchFilters, PaginatedResponse } from '@/types';

export interface SearchOptions extends SearchFilters {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

class SearchService {
  // Main search function
  async search(
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    if (!query.trim()) {
      return [];
    }

    const params = {
      q: query,
      ...options,
    };

    return apiClient.get('/search', { params });
  }

  // Get search suggestions
  async getSuggestions(query: string): Promise<string[]> {
    if (!query.trim() || query.length < 2) {
      return [];
    }

    return apiClient.get('/search/suggestions', {
      params: { q: query }
    });
  }

  // Advanced search with pagination
  async advancedSearch(
    query: string,
    options: SearchOptions = {}
  ): Promise<PaginatedResponse<SearchResult>> {
    const params = {
      q: query,
      page: options.page || 1,
      limit: options.limit || 20,
      ...options,
    };

    return apiClient.get('/search/advanced', { params });
  }

  // Search by type
  async searchByType(
    type: string,
    query?: string,
    options: Omit<SearchOptions, 'types'> = {}
  ): Promise<SearchResult[]> {
    const params = {
      type,
      q: query,
      ...options,
    };

    return apiClient.get('/search/by-type', { params });
  }

  // Semantic search (using embeddings)
  async semanticSearch(
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    const params = {
      q: query,
      ...options,
    };

    return apiClient.get('/search/semantic', { params });
  }

  // Search within specific sources
  async searchInSources(
    query: string,
    sources: string[],
    options: Omit<SearchOptions, 'sources'> = {}
  ): Promise<SearchResult[]> {
    const params = {
      q: query,
      sources: sources.join(','),
      ...options,
    };

    return apiClient.get('/search/sources', { params });
  }

  // Get popular searches
  async getPopularSearches(limit: number = 10): Promise<string[]> {
    return apiClient.get('/search/popular', {
      params: { limit }
    });
  }

  // Get recent searches (user-specific if authenticated)
  async getRecentSearches(limit: number = 10): Promise<string[]> {
    return apiClient.get('/search/recent', {
      params: { limit }
    });
  }

  // Save search query (for analytics)
  async saveSearch(query: string, resultCount: number): Promise<void> {
    return apiClient.post('/search/save', {
      query,
      resultCount,
      timestamp: new Date().toISOString(),
    });
  }

  // Get search analytics
  async getSearchAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<{
    totalSearches: number;
    uniqueQueries: number;
    averageResultCount: number;
    topQueries: Array<{ query: string; count: number }>;
    searchTrends: Array<{ date: string; count: number }>;
  }> {
    const params: any = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;

    return apiClient.get('/search/analytics', { params });
  }

  // Auto-complete search
  async autocomplete(query: string, limit: number = 5): Promise<string[]> {
    if (!query.trim() || query.length < 2) {
      return [];
    }

    return apiClient.get('/search/autocomplete', {
      params: { q: query, limit }
    });
  }

  // Search filters and facets
  async getSearchFilters(query?: string): Promise<{
    types: Array<{ value: string; count: number }>;
    sources: Array<{ value: string; count: number }>;
    dateRanges: Array<{ label: string; value: string; count: number }>;
  }> {
    const params = query ? { q: query } : {};
    return apiClient.get('/search/filters', { params });
  }

  // Similar content search
  async findSimilar(
    contentId: string,
    contentType: string,
    limit: number = 10
  ): Promise<SearchResult[]> {
    return apiClient.get('/search/similar', {
      params: { id: contentId, type: contentType, limit }
    });
  }

  // Search within date range
  async searchByDateRange(
    query: string,
    startDate: string,
    endDate: string,
    options: Omit<SearchOptions, 'dateRange'> = {}
  ): Promise<SearchResult[]> {
    const params = {
      q: query,
      startDate,
      endDate,
      ...options,
    };

    return apiClient.get('/search/date-range', { params });
  }

  // Export search results
  async exportResults(
    query: string,
    options: SearchOptions = {},
    format: 'json' | 'csv' | 'xlsx' = 'json'
  ): Promise<Blob> {
    const params = {
      q: query,
      format,
      ...options,
    };

    const response = await fetch(`${apiClient.defaults.baseURL}/search/export?${new URLSearchParams(params)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('Export failed');
    }

    return response.blob();
  }

  // Saved searches
  async saveSearchQuery(
    name: string,
    query: string,
    filters: SearchFilters
  ): Promise<{ id: string; name: string; query: string; filters: SearchFilters; createdAt: string }> {
    return apiClient.post('/search/saved', {
      name,
      query,
      filters,
    });
  }

  async getSavedSearches(): Promise<Array<{
    id: string;
    name: string;
    query: string;
    filters: SearchFilters;
    createdAt: string;
    lastUsed?: string;
  }>> {
    return apiClient.get('/search/saved');
  }

  async deleteSavedSearch(id: string): Promise<void> {
    return apiClient.delete(`/search/saved/${id}`);
  }

  async executeSavedSearch(id: string): Promise<SearchResult[]> {
    return apiClient.get(`/search/saved/${id}/execute`);
  }

  // Search history
  async getSearchHistory(limit: number = 50): Promise<Array<{
    query: string;
    timestamp: string;
    resultCount: number;
  }>> {
    return apiClient.get('/search/history', {
      params: { limit }
    });
  }

  async clearSearchHistory(): Promise<void> {
    return apiClient.delete('/search/history');
  }
}

// Create and export service instance
export const searchService = new SearchService();
export default searchService;
