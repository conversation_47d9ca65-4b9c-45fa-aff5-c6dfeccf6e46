import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Upload,
  File,
  FileText,
  Image,
  Download,
  Trash2,
  Eye,
  CheckCircle,
  AlertCircle,
  Clock,
  Plus,
  Link,
} from 'lucide-react';
import toast from 'react-hot-toast';

import Button from '@/components/atoms/Button';
import Input from '@/components/atoms/Input';
import Card from '@/components/atoms/Card';
import Badge from '@/components/atoms/Badge';
import LoadingSpinner from '@/components/atoms/LoadingSpinner';

import { UploadedFile, UploadOptions } from '@/types';
import { uploadService } from '@/services/uploadService';

const UploadPage: React.FC = () => {
  const [uploadOptions, setUploadOptions] = useState<UploadOptions>({
    extractText: true,
    addToKnowledgeBase: true,
    source: 'manual_upload',
  });
  const [urlToUpload, setUrlToUpload] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const queryClient = useQueryClient();

  // Fetch uploaded files
  const {
    data: files,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['uploaded-files'],
    queryFn: () => uploadService.listFiles({ page: 1, limit: 50 }),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Fetch upload statistics
  const { data: uploadStats } = useQuery({
    queryKey: ['upload-stats'],
    queryFn: () => uploadService.getStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // File upload mutation
  const uploadMutation = useMutation({
    mutationFn: (files: File[]) => uploadService.uploadFiles(files, uploadOptions),
    onSuccess: (data) => {
      toast.success(`Successfully uploaded ${data.successCount} files`);
      if (data.errorCount > 0) {
        toast.error(`Failed to upload ${data.errorCount} files`);
      }
      queryClient.invalidateQueries({ queryKey: ['uploaded-files'] });
      queryClient.invalidateQueries({ queryKey: ['upload-stats'] });
    },
    onError: (error: any) => {
      toast.error(`Upload failed: ${error.message}`);
    },
  });

  // URL upload mutation
  const urlUploadMutation = useMutation({
    mutationFn: (url: string) => uploadService.uploadFromUrl(url, uploadOptions),
    onSuccess: () => {
      toast.success('Successfully uploaded from URL');
      setUrlToUpload('');
      queryClient.invalidateQueries({ queryKey: ['uploaded-files'] });
      queryClient.invalidateQueries({ queryKey: ['upload-stats'] });
    },
    onError: (error: any) => {
      toast.error(`URL upload failed: ${error.message}`);
    },
  });

  // Delete file mutation
  const deleteMutation = useMutation({
    mutationFn: (fileId: string) => uploadService.deleteFile(fileId),
    onSuccess: () => {
      toast.success('File deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['uploaded-files'] });
      queryClient.invalidateQueries({ queryKey: ['upload-stats'] });
    },
    onError: (error: any) => {
      toast.error(`Delete failed: ${error.message}`);
    },
  });

  // Batch operations mutation
  const batchMutation = useMutation({
    mutationFn: ({ fileIds, operation }: { fileIds: string[]; operation: string }) =>
      uploadService.batchProcess(fileIds, operation, {}),
    onSuccess: (data) => {
      toast.success(`Batch operation completed: ${data.successCount} successful, ${data.errorCount} failed`);
      queryClient.invalidateQueries({ queryKey: ['uploaded-files'] });
    },
    onError: (error: any) => {
      toast.error(`Batch operation failed: ${error.message}`);
    },
  });

  // Dropzone configuration
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      uploadMutation.mutate(acceptedFiles);
    }
  }, [uploadMutation]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/json': ['.json'],
      'text/csv': ['.csv'],
      'application/xml': ['.xml'],
      'text/xml': ['.xml'],
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    multiple: true,
  });

  // Handle URL upload
  const handleUrlUpload = () => {
    if (urlToUpload.trim()) {
      urlUploadMutation.mutate(urlToUpload.trim());
    }
  };

  // Handle file selection
  const handleFileSelect = (fileId: string, selected: boolean) => {
    setSelectedFiles(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(fileId);
      } else {
        newSet.delete(fileId);
      }
      return newSet;
    });
  };

  // Handle batch operations
  const handleBatchOperation = (operation: string) => {
    if (selectedFiles.size > 0) {
      batchMutation.mutate({
        fileIds: Array.from(selectedFiles),
        operation,
      });
      setSelectedFiles(new Set());
    }
  };

  // Get file icon
  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <Image className="w-5 h-5" />;
    if (mimeType.includes('pdf')) return <FileText className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  // Get file status badge
  const getStatusBadge = (file: UploadedFile) => {
    if (!file.processed) {
      return <Badge variant="warning" size="sm">Processing</Badge>;
    }
    if (file.addedToKnowledgeBase) {
      return <Badge variant="success" size="sm">In Knowledge Base</Badge>;
    }
    return <Badge variant="secondary" size="sm">Processed</Badge>;
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Upload Documents
        </h1>
        <p className="text-gray-600">
          Upload documents to extract knowledge and add to the knowledge base
        </p>
      </div>

      {/* Upload Statistics */}
      {uploadStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card padding="md">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{uploadStats.totalFiles}</div>
              <div className="text-sm text-gray-600">Total Files</div>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{uploadStats.processedFiles}</div>
              <div className="text-sm text-gray-600">Processed</div>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{uploadStats.knowledgeBaseFiles}</div>
              <div className="text-sm text-gray-600">In Knowledge Base</div>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(uploadStats.processingRate)}%
              </div>
              <div className="text-sm text-gray-600">Processing Rate</div>
            </div>
          </Card>
        </div>
      )}

      {/* Upload Options */}
      <Card padding="lg" className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Upload Options</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={uploadOptions.extractText}
              onChange={(e) => setUploadOptions(prev => ({
                ...prev,
                extractText: e.target.checked
              }))}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Extract text content</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={uploadOptions.addToKnowledgeBase}
              onChange={(e) => setUploadOptions(prev => ({
                ...prev,
                addToKnowledgeBase: e.target.checked
              }))}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Add to knowledge base</span>
          </label>
          <Input
            placeholder="Source (optional)"
            value={uploadOptions.source || ''}
            onChange={(e) => setUploadOptions(prev => ({
              ...prev,
              source: e.target.value
            }))}
            size="sm"
          />
        </div>

        {/* File Upload Area */}
        <div
          {...getRootProps()}
          className={`upload-area ${isDragActive ? 'dragover' : ''} mb-6`}
        >
          <input {...getInputProps()} />
          <div className="text-center">
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            {isDragActive ? (
              <p className="text-lg text-primary-600 font-medium">
                Drop files here to upload
              </p>
            ) : (
              <>
                <p className="text-lg text-gray-900 font-medium mb-2">
                  Drag & drop files here, or click to select
                </p>
                <p className="text-sm text-gray-600">
                  Supports PDF, DOC, DOCX, TXT, JSON, CSV, XML files up to 50MB
                </p>
              </>
            )}
          </div>
        </div>

        {/* URL Upload */}
        <div className="border-t border-gray-200 pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Upload from URL</h3>
          <div className="flex space-x-4">
            <Input
              placeholder="Enter URL to upload content from..."
              value={urlToUpload}
              onChange={(e) => setUrlToUpload(e.target.value)}
              leftIcon={<Link className="w-4 h-4" />}
              className="flex-1"
            />
            <Button
              onClick={handleUrlUpload}
              loading={urlUploadMutation.isPending}
              disabled={!urlToUpload.trim()}
              leftIcon={<Upload className="w-4 h-4" />}
            >
              Upload
            </Button>
          </div>
        </div>
      </Card>

      {/* Files List */}
      <Card padding="lg">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Uploaded Files</h2>
          <div className="flex items-center space-x-2">
            {selectedFiles.size > 0 && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchOperation('addToKnowledgeBase')}
                  leftIcon={<Plus className="w-4 h-4" />}
                >
                  Add to KB ({selectedFiles.size})
                </Button>
                <Button
                  variant="error"
                  size="sm"
                  onClick={() => handleBatchOperation('delete')}
                  leftIcon={<Trash2 className="w-4 h-4" />}
                >
                  Delete ({selectedFiles.size})
                </Button>
              </>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              leftIcon={<Download className="w-4 h-4" />}
            >
              Refresh
            </Button>
          </div>
        </div>

        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" text="Loading files..." />
          </div>
        )}

        {error && (
          <div className="text-center py-12">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-gray-600">Failed to load files</p>
          </div>
        )}

        {files && files.files.length === 0 && (
          <div className="text-center py-12">
            <File className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No files uploaded yet</p>
          </div>
        )}

        {files && files.files.length > 0 && (
          <div className="space-y-3">
            {files.files.map((file) => (
              <motion.div
                key={file.id}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div className="flex items-center space-x-4">
                  <input
                    type="checkbox"
                    checked={selectedFiles.has(file.id)}
                    onChange={(e) => handleFileSelect(file.id, e.target.checked)}
                    className="rounded"
                  />
                  <div className="text-gray-400">
                    {getFileIcon(file.mimeType)}
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{file.originalName}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>{(file.size / 1024 / 1024).toFixed(2)} MB</span>
                      <span>{new Date(file.createdAt).toLocaleDateString()}</span>
                      {file.source && <span>Source: {file.source}</span>}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  {getStatusBadge(file)}
                  <div className="flex items-center space-x-1">
                    <Button variant="ghost" size="sm" title="View">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteMutation.mutate(file.id)}
                      loading={deleteMutation.isPending}
                      title="Delete"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default UploadPage;
