import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Settings,
  User,
  Bell,
  Shield,
  Database,
  Palette,
  Download,
  Upload,
  Trash2,
  Save,
  RefreshCw,
} from 'lucide-react';

import Button from '@/components/atoms/Button';
import Input from '@/components/atoms/Input';
import Card from '@/components/atoms/Card';
import Badge from '@/components/atoms/Badge';

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      appName: 'Suplementor',
      language: 'en',
      timezone: 'UTC',
      theme: 'light',
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: false,
      searchAlerts: true,
      uploadAlerts: true,
      systemAlerts: true,
    },
    privacy: {
      dataCollection: true,
      analytics: true,
      crashReports: true,
      shareUsageData: false,
    },
    graph: {
      defaultNodeSize: 8,
      defaultLinkDistance: 100,
      defaultChargeStrength: -300,
      showLabels: true,
      showRelationshipLabels: false,
      maxNodes: 10000,
      maxRelationships: 50000,
    },
    ai: {
      defaultModel: 'gemini-pro',
      temperature: 0.7,
      maxTokens: 4096,
      enableAutoExpansion: true,
      confidenceThreshold: 0.7,
    },
  });

  const tabs = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'privacy', name: 'Privacy', icon: Shield },
    { id: 'graph', name: 'Graph', icon: Database },
    { id: 'ai', name: 'AI Settings', icon: Palette },
  ];

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  const handleSaveSettings = () => {
    // Save settings to backend
    console.log('Saving settings:', settings);
    // Show success toast
  };

  const handleResetSettings = () => {
    // Reset to default settings
    console.log('Resetting settings');
  };

  const handleExportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'suplementor-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string);
          setSettings(importedSettings);
        } catch (error) {
          console.error('Failed to import settings:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Application Name
        </label>
        <Input
          value={settings.general.appName}
          onChange={(e) => handleSettingChange('general', 'appName', e.target.value)}
          placeholder="Application name"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Language
        </label>
        <select
          value={settings.general.language}
          onChange={(e) => handleSettingChange('general', 'language', e.target.value)}
          className="input"
        >
          <option value="en">English</option>
          <option value="pl">Polski</option>
          <option value="es">Español</option>
          <option value="fr">Français</option>
          <option value="de">Deutsch</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Timezone
        </label>
        <select
          value={settings.general.timezone}
          onChange={(e) => handleSettingChange('general', 'timezone', e.target.value)}
          className="input"
        >
          <option value="UTC">UTC</option>
          <option value="America/New_York">Eastern Time</option>
          <option value="America/Los_Angeles">Pacific Time</option>
          <option value="Europe/London">London</option>
          <option value="Europe/Warsaw">Warsaw</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Theme
        </label>
        <div className="flex space-x-4">
          {['light', 'dark', 'auto'].map(theme => (
            <label key={theme} className="flex items-center">
              <input
                type="radio"
                name="theme"
                value={theme}
                checked={settings.general.theme === theme}
                onChange={(e) => handleSettingChange('general', 'theme', e.target.value)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700 capitalize">{theme}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {Object.entries(settings.notifications).map(([key, value]) => (
        <div key={key} className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-900 capitalize">
              {key.replace(/([A-Z])/g, ' $1').trim()}
            </p>
            <p className="text-sm text-gray-500">
              {key === 'emailNotifications' && 'Receive notifications via email'}
              {key === 'pushNotifications' && 'Receive browser push notifications'}
              {key === 'searchAlerts' && 'Get notified about search results'}
              {key === 'uploadAlerts' && 'Get notified about upload status'}
              {key === 'systemAlerts' && 'Receive system status notifications'}
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={value as boolean}
              onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
          </label>
        </div>
      ))}
    </div>
  );

  const renderPrivacySettings = () => (
    <div className="space-y-6">
      {Object.entries(settings.privacy).map(([key, value]) => (
        <div key={key} className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-900 capitalize">
              {key.replace(/([A-Z])/g, ' $1').trim()}
            </p>
            <p className="text-sm text-gray-500">
              {key === 'dataCollection' && 'Allow collection of usage data for improvements'}
              {key === 'analytics' && 'Enable analytics tracking'}
              {key === 'crashReports' && 'Send crash reports to help fix issues'}
              {key === 'shareUsageData' && 'Share anonymized usage data with third parties'}
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={value as boolean}
              onChange={(e) => handleSettingChange('privacy', key, e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
          </label>
        </div>
      ))}
    </div>
  );

  const renderGraphSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Default Node Size
        </label>
        <Input
          type="number"
          value={settings.graph.defaultNodeSize}
          onChange={(e) => handleSettingChange('graph', 'defaultNodeSize', parseInt(e.target.value))}
          min={4}
          max={20}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Default Link Distance
        </label>
        <Input
          type="number"
          value={settings.graph.defaultLinkDistance}
          onChange={(e) => handleSettingChange('graph', 'defaultLinkDistance', parseInt(e.target.value))}
          min={50}
          max={300}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Default Charge Strength
        </label>
        <Input
          type="number"
          value={settings.graph.defaultChargeStrength}
          onChange={(e) => handleSettingChange('graph', 'defaultChargeStrength', parseInt(e.target.value))}
          min={-1000}
          max={-100}
        />
      </div>

      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-900">Show Node Labels</p>
          <p className="text-sm text-gray-500">Display labels on graph nodes by default</p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.graph.showLabels}
            onChange={(e) => handleSettingChange('graph', 'showLabels', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
        </label>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-900">Show Relationship Labels</p>
          <p className="text-sm text-gray-500">Display labels on graph relationships by default</p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.graph.showRelationshipLabels}
            onChange={(e) => handleSettingChange('graph', 'showRelationshipLabels', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
        </label>
      </div>
    </div>
  );

  const renderAISettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Default AI Model
        </label>
        <select
          value={settings.ai.defaultModel}
          onChange={(e) => handleSettingChange('ai', 'defaultModel', e.target.value)}
          className="input"
        >
          <option value="gemini-pro">Gemini Pro</option>
          <option value="gpt-4">GPT-4</option>
          <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
          <option value="claude-3">Claude 3</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Temperature ({settings.ai.temperature})
        </label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={settings.ai.temperature}
          onChange={(e) => handleSettingChange('ai', 'temperature', parseFloat(e.target.value))}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>Conservative</span>
          <span>Creative</span>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Max Tokens
        </label>
        <Input
          type="number"
          value={settings.ai.maxTokens}
          onChange={(e) => handleSettingChange('ai', 'maxTokens', parseInt(e.target.value))}
          min={1000}
          max={8000}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Confidence Threshold ({settings.ai.confidenceThreshold})
        </label>
        <input
          type="range"
          min="0.1"
          max="1"
          step="0.1"
          value={settings.ai.confidenceThreshold}
          onChange={(e) => handleSettingChange('ai', 'confidenceThreshold', parseFloat(e.target.value))}
          className="w-full"
        />
      </div>

      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-900">Enable Auto Expansion</p>
          <p className="text-sm text-gray-500">Automatically expand graph nodes with AI suggestions</p>
        </div>
        <label className="relative inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            checked={settings.ai.enableAutoExpansion}
            onChange={(e) => handleSettingChange('ai', 'enableAutoExpansion', e.target.checked)}
            className="sr-only peer"
          />
          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
        </label>
      </div>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
        <p className="text-gray-600">
          Customize your Suplementor experience
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Sidebar */}
        <div className="lg:w-64">
          <Card padding="sm">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-500'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </Card>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <Card padding="lg">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                {tabs.find(tab => tab.id === activeTab)?.name}
              </h2>
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportSettings}
                  className="hidden"
                  id="import-settings"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('import-settings')?.click()}
                  leftIcon={<Upload className="w-4 h-4" />}
                >
                  Import
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportSettings}
                  leftIcon={<Download className="w-4 h-4" />}
                >
                  Export
                </Button>
              </div>
            </div>

            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2 }}
            >
              {activeTab === 'general' && renderGeneralSettings()}
              {activeTab === 'notifications' && renderNotificationSettings()}
              {activeTab === 'privacy' && renderPrivacySettings()}
              {activeTab === 'graph' && renderGraphSettings()}
              {activeTab === 'ai' && renderAISettings()}
            </motion.div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-6 mt-6 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={handleResetSettings}
                leftIcon={<RefreshCw className="w-4 h-4" />}
              >
                Reset to Defaults
              </Button>
              <div className="flex items-center space-x-3">
                <Button variant="outline">
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSaveSettings}
                  leftIcon={<Save className="w-4 h-4" />}
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
