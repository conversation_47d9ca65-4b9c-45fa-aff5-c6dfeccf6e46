import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  BarChart3,
  TrendingUp,
  Users,
  Database,
  Activity,
  Download,
  RefreshCw,
  Calendar,
  Filter,
} from 'lucide-react';

import Button from '@/components/atoms/Button';
import Card from '@/components/atoms/Card';
import Badge from '@/components/atoms/Badge';
import LoadingSpinner from '@/components/atoms/LoadingSpinner';
import Input from '@/components/atoms/Input';

import { graphService } from '@/services/graphService';
import { searchService } from '@/services/searchService';
import { uploadService } from '@/services/uploadService';

const AnalyticsPage: React.FC = () => {
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end: new Date().toISOString().split('T')[0], // today
  });

  // Fetch analytics data
  const { data: graphStats, isLoading: graphLoading } = useQuery({
    queryKey: ['graph-stats'],
    queryFn: () => graphService.getGraphStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: searchAnalytics, isLoading: searchLoading } = useQuery({
    queryKey: ['search-analytics', dateRange],
    queryFn: () => searchService.getSearchAnalytics(dateRange.start, dateRange.end),
    staleTime: 5 * 60 * 1000,
  });

  const { data: uploadStats, isLoading: uploadLoading } = useQuery({
    queryKey: ['upload-stats'],
    queryFn: () => uploadService.getStats(),
    staleTime: 5 * 60 * 1000,
  });

  const { data: uploadHistory } = useQuery({
    queryKey: ['upload-history', dateRange],
    queryFn: () => uploadService.getUploadHistory(dateRange.start, dateRange.end),
    staleTime: 5 * 60 * 1000,
  });

  const isLoading = graphLoading || searchLoading || uploadLoading;

  // Calculate growth metrics
  const getGrowthMetric = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Analytics</h1>
          <p className="text-gray-600">
            Insights into your knowledge graph usage and performance
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              size="sm"
            />
            <span className="text-gray-500">to</span>
            <Input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              size="sm"
            />
          </div>
          <Button
            variant="outline"
            leftIcon={<Download className="w-4 h-4" />}
          >
            Export
          </Button>
        </div>
      </div>

      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" text="Loading analytics..." />
        </div>
      )}

      {!isLoading && (
        <div className="space-y-8">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Graph Nodes */}
            <Card padding="lg" animate hover>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Nodes</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {graphStats?.totalNodes?.toLocaleString() || 0}
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600">+12.5%</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Database className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </Card>

            {/* Graph Relationships */}
            <Card padding="lg" animate hover>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Relationships</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {graphStats?.totalRelationships?.toLocaleString() || 0}
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600">+8.3%</span>
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <Activity className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </Card>

            {/* Total Searches */}
            <Card padding="lg" animate hover>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Searches</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {searchAnalytics?.totalSearches?.toLocaleString() || 0}
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-blue-500 mr-1" />
                    <span className="text-sm text-blue-600">+15.2%</span>
                  </div>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </Card>

            {/* Uploaded Files */}
            <Card padding="lg" animate hover>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Uploaded Files</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {uploadStats?.totalFiles?.toLocaleString() || 0}
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-orange-500 mr-1" />
                    <span className="text-sm text-orange-600">+22.1%</span>
                  </div>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <Users className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </Card>
          </div>

          {/* Charts Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Node Types Distribution */}
            <Card padding="lg">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Node Types</h3>
                <Button variant="ghost" size="sm">
                  <RefreshCw className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-4">
                {graphStats?.nodesByType?.map((nodeType, index) => (
                  <div key={nodeType.label} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{
                          backgroundColor: [
                            '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ef4444'
                          ][index % 5]
                        }}
                      />
                      <span className="text-sm font-medium text-gray-900">
                        {nodeType.label}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">
                        {nodeType.count.toLocaleString()}
                      </span>
                      <Badge variant="secondary" size="sm">
                        {Math.round((nodeType.count / (graphStats?.totalNodes || 1)) * 100)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Search Trends */}
            <Card padding="lg">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Search Activity</h3>
                <Button variant="ghost" size="sm">
                  <Filter className="w-4 h-4" />
                </Button>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Total Searches</span>
                  <span className="text-lg font-semibold text-gray-900">
                    {searchAnalytics?.totalSearches?.toLocaleString() || 0}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Unique Queries</span>
                  <span className="text-lg font-semibold text-gray-900">
                    {searchAnalytics?.uniqueQueries?.toLocaleString() || 0}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Avg Results per Search</span>
                  <span className="text-lg font-semibold text-gray-900">
                    {searchAnalytics?.averageResultCount?.toFixed(1) || 0}
                  </span>
                </div>
              </div>
            </Card>
          </div>

          {/* Top Queries */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card padding="lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Top Search Queries</h3>
              <div className="space-y-3">
                {searchAnalytics?.topQueries?.slice(0, 10).map((query, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-500 w-6">
                        {index + 1}
                      </span>
                      <span className="text-sm text-gray-900">{query.query}</span>
                    </div>
                    <Badge variant="secondary" size="sm">
                      {query.count}
                    </Badge>
                  </div>
                )) || (
                  <p className="text-gray-500 text-center py-4">No search data available</p>
                )}
              </div>
            </Card>

            {/* Upload Statistics */}
            <Card padding="lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Upload Statistics</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Total Files</span>
                  <span className="text-lg font-semibold text-gray-900">
                    {uploadStats?.totalFiles?.toLocaleString() || 0}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Processed Files</span>
                  <span className="text-lg font-semibold text-gray-900">
                    {uploadStats?.processedFiles?.toLocaleString() || 0}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">In Knowledge Base</span>
                  <span className="text-lg font-semibold text-gray-900">
                    {uploadStats?.knowledgeBaseFiles?.toLocaleString() || 0}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Processing Rate</span>
                  <Badge variant="success" size="sm">
                    {Math.round(uploadStats?.processingRate || 0)}%
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Total Size</span>
                  <span className="text-lg font-semibold text-gray-900">
                    {((uploadStats?.sizeStats?.totalSize || 0) / 1024 / 1024 / 1024).toFixed(2)} GB
                  </span>
                </div>
              </div>
            </Card>
          </div>

          {/* File Types */}
          <Card padding="lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">File Types Distribution</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {uploadStats?.typeStats?.map((type, index) => (
                <div key={type._id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{type._id || 'Unknown'}</p>
                    <p className="text-sm text-gray-600">
                      {((type.totalSize || 0) / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  <Badge variant="primary" size="sm">
                    {type.count}
                  </Badge>
                </div>
              )) || (
                <p className="text-gray-500 text-center py-4 col-span-3">No file type data available</p>
              )}
            </div>
          </Card>

          {/* System Health */}
          <Card padding="lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">System Health</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Activity className="w-8 h-8 text-green-600" />
                </div>
                <p className="text-sm font-medium text-gray-900">API Status</p>
                <Badge variant="success" size="sm" className="mt-1">Operational</Badge>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Database className="w-8 h-8 text-blue-600" />
                </div>
                <p className="text-sm font-medium text-gray-900">Database</p>
                <Badge variant="success" size="sm" className="mt-1">Healthy</Badge>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <BarChart3 className="w-8 h-8 text-purple-600" />
                </div>
                <p className="text-sm font-medium text-gray-900">Performance</p>
                <Badge variant="success" size="sm" className="mt-1">Optimal</Badge>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AnalyticsPage;
