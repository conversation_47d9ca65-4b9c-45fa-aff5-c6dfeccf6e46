import React, { useState, useCallback } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Globe, 
  Brain, 
  FileText, 
  Loader2, 
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Download,
  Filter
} from 'lucide-react';
import { toast } from 'react-hot-toast';

// Import ReactBits components (will be implemented)
import { AnimatedCard } from '../components/ui/AnimatedCard';
import { SearchBar } from '../components/ui/SearchBar';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { ProgressIndicator } from '../components/ui/ProgressIndicator';

// API types
interface ResearchQuery {
  query: string;
  type: 'web_search' | 'academic' | 'news' | 'supplement_specific';
  filters?: {
    maxResults?: number;
    timeRange?: 'day' | 'week' | 'month' | 'year';
    domains?: string[];
  };
}

interface ResearchResult {
  title: string;
  url: string;
  snippet: string;
  content?: string;
  publishedDate?: string;
  domain: string;
  relevanceScore: number;
  medicalRelevance?: number;
}

interface MedicalAnalysis {
  analysis: string;
  entities: Array<{
    name: string;
    type: string;
    confidence: number;
  }>;
  interactions: Array<{
    substance1: string;
    substance2: string;
    interactionType: string;
    severity: number;
  }>;
  recommendations: string[];
  confidence: number;
  reasoning: string[];
}

const ResearchPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState<ResearchQuery['type']>('supplement_specific');
  const [selectedFilters, setSelectedFilters] = useState({
    maxResults: 10,
    timeRange: 'month' as const,
    includeAnalysis: true
  });
  const [activeTab, setActiveTab] = useState<'search' | 'crawl' | 'analysis'>('search');

  // Web Search Mutation
  const searchMutation = useMutation({
    mutationFn: async (query: ResearchQuery) => {
      const response = await fetch('/api/research/web-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(query)
      });
      if (!response.ok) throw new Error('Search failed');
      return response.json();
    },
    onSuccess: () => {
      toast.success('Search completed successfully!');
    },
    onError: (error) => {
      toast.error(`Search failed: ${error.message}`);
    }
  });

  // Comprehensive Research Mutation
  const comprehensiveResearchMutation = useMutation({
    mutationFn: async (params: { query: string; includeAnalysis: boolean; maxResults: number }) => {
      const response = await fetch('/api/research/comprehensive-research', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      if (!response.ok) throw new Error('Research failed');
      return response.json();
    },
    onSuccess: () => {
      toast.success('Comprehensive research completed!');
    },
    onError: (error) => {
      toast.error(`Research failed: ${error.message}`);
    }
  });

  // Website Crawl Mutation
  const crawlMutation = useMutation({
    mutationFn: async (params: { url: string; type: string; options?: any }) => {
      const response = await fetch('/api/research/crawl-website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      if (!response.ok) throw new Error('Crawl failed');
      return response.json();
    },
    onSuccess: () => {
      toast.success('Website crawl completed!');
    },
    onError: (error) => {
      toast.error(`Crawl failed: ${error.message}`);
    }
  });

  // Medical Analysis Mutation
  const analysisMutation = useMutation({
    mutationFn: async (params: { text: string; analysisType: string; context?: string }) => {
      const response = await fetch('/api/research/medical-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      if (!response.ok) throw new Error('Analysis failed');
      return response.json();
    },
    onSuccess: () => {
      toast.success('Medical analysis completed!');
    },
    onError: (error) => {
      toast.error(`Analysis failed: ${error.message}`);
    }
  });

  const handleSearch = useCallback(() => {
    if (!searchQuery.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    const query: ResearchQuery = {
      query: searchQuery,
      type: searchType,
      filters: {
        maxResults: selectedFilters.maxResults,
        timeRange: selectedFilters.timeRange
      }
    };

    searchMutation.mutate(query);
  }, [searchQuery, searchType, selectedFilters, searchMutation]);

  const handleComprehensiveResearch = useCallback(() => {
    if (!searchQuery.trim()) {
      toast.error('Please enter a research query');
      return;
    }

    comprehensiveResearchMutation.mutate({
      query: searchQuery,
      includeAnalysis: selectedFilters.includeAnalysis,
      maxResults: selectedFilters.maxResults
    });
  }, [searchQuery, selectedFilters, comprehensiveResearchMutation]);

  const isLoading = searchMutation.isPending || 
                   comprehensiveResearchMutation.isPending || 
                   crawlMutation.isPending || 
                   analysisMutation.isPending;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🔬 Medical Research & AI Analysis
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Powered by Gemma3-4B-Medical, Brave Search, and Crawl4AI for comprehensive supplement research
          </p>
        </motion.div>

        {/* Search Interface */}
        <AnimatedCard className="mb-8">
          <div className="p-6">
            <SearchBar
              value={searchQuery}
              onChange={setSearchQuery}
              onSearch={handleSearch}
              placeholder="Search for supplements, ingredients, or medical information..."
              className="mb-4"
            />

            {/* Search Type Selector */}
            <div className="flex flex-wrap gap-2 mb-4">
              {[
                { value: 'supplement_specific', label: '💊 Supplements', icon: '💊' },
                { value: 'academic', label: '📚 Academic', icon: '📚' },
                { value: 'web_search', label: '🌐 Web Search', icon: '🌐' },
                { value: 'news', label: '📰 News', icon: '📰' }
              ].map((type) => (
                <motion.button
                  key={type.value}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setSearchType(type.value as ResearchQuery['type'])}
                  className={`px-4 py-2 rounded-lg font-medium transition-all ${
                    searchType === type.value
                      ? 'bg-blue-500 text-white shadow-lg'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {type.icon} {type.label}
                </motion.button>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleSearch}
                disabled={isLoading}
                className="flex items-center gap-2 px-6 py-3 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Search className="w-4 h-4" />}
                Quick Search
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleComprehensiveResearch}
                disabled={isLoading}
                className="flex items-center gap-2 px-6 py-3 bg-purple-500 text-white rounded-lg font-medium hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Brain className="w-4 h-4" />}
                Comprehensive Research
              </motion.button>
            </div>
          </div>
        </AnimatedCard>

        {/* Loading State */}
        <AnimatePresence>
          {isLoading && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="mb-8"
            >
              <AnimatedCard>
                <div className="p-8 text-center">
                  <LoadingSpinner size="large" />
                  <h3 className="text-lg font-semibold text-gray-900 mt-4 mb-2">
                    Processing Your Research Request
                  </h3>
                  <p className="text-gray-600">
                    Searching the web, analyzing content, and generating medical insights...
                  </p>
                  <ProgressIndicator 
                    progress={searchMutation.isPending ? 25 : comprehensiveResearchMutation.isPending ? 75 : 50}
                    className="mt-4"
                  />
                </div>
              </AnimatedCard>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Results Display */}
        <AnimatePresence>
          {(searchMutation.data || comprehensiveResearchMutation.data) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <ResultsDisplay 
                data={searchMutation.data || comprehensiveResearchMutation.data}
                type={searchMutation.data ? 'search' : 'comprehensive'}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Health Check Status */}
        <HealthStatus />
      </div>
    </div>
  );
};

// Results Display Component
const ResultsDisplay: React.FC<{ data: any; type: 'search' | 'comprehensive' }> = ({ data, type }) => {
  if (!data?.data) return null;

  const results = type === 'search' ? data.data.results : data.data.searchResults?.results || [];
  const medicalAnalysis = type === 'comprehensive' ? data.data.medicalAnalysis : null;

  return (
    <div className="space-y-6">
      {/* Search Results */}
      {results && results.length > 0 && (
        <AnimatedCard>
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Search className="w-6 h-6" />
              Search Results ({results.length})
            </h2>
            <div className="grid gap-4">
              {results.map((result: ResearchResult, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{result.title}</h3>
                      <p className="text-gray-600 text-sm mb-2">{result.snippet}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>{result.domain}</span>
                        {result.publishedDate && <span>{result.publishedDate}</span>}
                        <span>Relevance: {Math.round(result.relevanceScore * 100)}%</span>
                        {result.medicalRelevance && (
                          <span>Medical: {Math.round(result.medicalRelevance * 100)}%</span>
                        )}
                      </div>
                    </div>
                    <a
                      href={result.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="ml-4 p-2 text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </AnimatedCard>
      )}

      {/* Medical Analysis */}
      {medicalAnalysis && (
        <AnimatedCard>
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Brain className="w-6 h-6" />
              Medical AI Analysis
            </h2>
            <MedicalAnalysisDisplay analysis={medicalAnalysis} />
          </div>
        </AnimatedCard>
      )}
    </div>
  );
};

// Medical Analysis Display Component
const MedicalAnalysisDisplay: React.FC<{ analysis: MedicalAnalysis }> = ({ analysis }) => {
  return (
    <div className="space-y-6">
      {/* Main Analysis */}
      <div>
        <h3 className="font-semibold text-gray-900 mb-2">Analysis</h3>
        <p className="text-gray-700 leading-relaxed">{analysis.analysis}</p>
      </div>

      {/* Entities */}
      {analysis.entities && analysis.entities.length > 0 && (
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">Identified Entities</h3>
          <div className="flex flex-wrap gap-2">
            {analysis.entities.map((entity, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
              >
                {entity.name} ({Math.round(entity.confidence * 100)}%)
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Recommendations */}
      {analysis.recommendations && analysis.recommendations.length > 0 && (
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">Recommendations</h3>
          <ul className="space-y-1">
            {analysis.recommendations.map((rec, index) => (
              <li key={index} className="flex items-start gap-2">
                <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700">{rec}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Confidence Score */}
      <div className="flex items-center gap-2">
        <span className="font-semibold text-gray-900">Confidence:</span>
        <div className="flex-1 bg-gray-200 rounded-full h-2">
          <div
            className="bg-green-500 h-2 rounded-full transition-all"
            style={{ width: `${analysis.confidence * 100}%` }}
          />
        </div>
        <span className="text-sm text-gray-600">{Math.round(analysis.confidence * 100)}%</span>
      </div>
    </div>
  );
};

// Health Status Component
const HealthStatus: React.FC = () => {
  const { data: healthData } = useQuery({
    queryKey: ['research-health'],
    queryFn: async () => {
      const response = await fetch('/api/research/health');
      if (!response.ok) throw new Error('Health check failed');
      return response.json();
    },
    refetchInterval: 30000, // Check every 30 seconds
  });

  if (!healthData) return null;

  const services = healthData.data?.services || {};
  const overall = healthData.data?.overall || false;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="mt-8"
    >
      <AnimatedCard>
        <div className="p-4">
          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            {overall ? (
              <CheckCircle className="w-5 h-5 text-green-500" />
            ) : (
              <AlertCircle className="w-5 h-5 text-red-500" />
            )}
            Service Status
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${services.search?.brave ? 'bg-green-500' : 'bg-red-500'}`} />
              Brave Search
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${services.search?.tavily ? 'bg-green-500' : 'bg-red-500'}`} />
              Tavily Search
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${services.crawl ? 'bg-green-500' : 'bg-red-500'}`} />
              Crawl4AI
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${services.medicalAI ? 'bg-green-500' : 'bg-red-500'}`} />
              Gemma3-4B Medical
            </div>
          </div>
        </div>
      </AnimatedCard>
    </motion.div>
  );
};

export default ResearchPage;
