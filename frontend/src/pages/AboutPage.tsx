import React from 'react';
import { motion } from 'framer-motion';
import {
  Heart,
  Github,
  ExternalLink,
  Mail,
  Users,
  Code,
  Database,
  Zap,
  Shield,
  Globe,
  Award,
  BookOpen,
} from 'lucide-react';

import Button from '@/components/atoms/Button';
import Card from '@/components/atoms/Card';
import Badge from '@/components/atoms/Badge';

const AboutPage: React.FC = () => {
  const features = [
    {
      icon: Database,
      title: 'Knowledge Graph',
      description: 'Interactive visualization of supplement relationships and interactions',
    },
    {
      icon: Zap,
      title: 'AI-Powered',
      description: 'Advanced AI for knowledge extraction and intelligent recommendations',
    },
    {
      icon: Shield,
      title: 'Evidence-Based',
      description: 'All information backed by scientific studies and research',
    },
    {
      icon: Globe,
      title: 'Open Source',
      description: 'Transparent, community-driven development',
    },
  ];

  const technologies = [
    { name: 'React', category: 'Frontend' },
    { name: 'TypeScript', category: 'Language' },
    { name: 'Node.js', category: 'Backend' },
    { name: 'Neo4j', category: 'Graph Database' },
    { name: '<PERSON><PERSON><PERSON>', category: 'Vector Database' },
    { name: '<PERSON>is', category: 'Cache' },
    { name: 'MongoDB', category: 'Document Database' },
    { name: 'D3.js', category: 'Visualization' },
    { name: 'Tailwind CSS', category: 'Styling' },
    { name: 'Gemini AI', category: 'AI/ML' },
  ];

  const team = [
    {
      name: 'Suplementor Team',
      role: 'Development Team',
      description: 'Passionate developers building the future of supplement knowledge',
      avatar: '👥',
    },
  ];

  const stats = [
    { label: 'Supplements', value: '10,000+' },
    { label: 'Ingredients', value: '5,000+' },
    { label: 'Studies', value: '50,000+' },
    { label: 'Interactions', value: '100,000+' },
  ];

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Database className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Suplementor Knowledge Graph
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            An interactive knowledge graph for supplement information, powered by AI and backed by scientific research. 
            Explore relationships between supplements, ingredients, effects, and studies in an intuitive visual interface.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Badge variant="primary" size="lg">
              Version 1.0.0
            </Badge>
            <Badge variant="success" size="lg">
              Open Source
            </Badge>
            <Badge variant="secondary" size="lg">
              MIT License
            </Badge>
          </div>
        </motion.div>
      </div>

      {/* Stats */}
      <motion.div
        className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {stats.map((stat, index) => (
          <Card key={index} padding="lg" className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">
              {stat.value}
            </div>
            <div className="text-sm text-gray-600">{stat.label}</div>
          </Card>
        ))}
      </motion.div>

      {/* Features */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
          Key Features
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} padding="lg" className="text-center" hover animate>
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Icon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {feature.description}
                </p>
              </Card>
            );
          })}
        </div>
      </motion.div>

      {/* Technology Stack */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
          Technology Stack
        </h2>
        <Card padding="lg">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {technologies.map((tech, index) => (
              <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="font-medium text-gray-900 mb-1">{tech.name}</div>
                <div className="text-xs text-gray-500">{tech.category}</div>
              </div>
            ))}
          </div>
        </Card>
      </motion.div>

      {/* Mission */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
      >
        <Card padding="lg" className="bg-gradient-to-r from-primary-50 to-blue-50">
          <div className="text-center">
            <Heart className="w-12 h-12 text-primary-600 mx-auto mb-4" />
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Mission</h2>
            <p className="text-lg text-gray-700 max-w-4xl mx-auto">
              To democratize access to supplement knowledge by creating an open, interactive platform 
              that visualizes the complex relationships between supplements, ingredients, effects, and 
              scientific research. We believe that informed decisions about health and nutrition should 
              be accessible to everyone.
            </p>
          </div>
        </Card>
      </motion.div>

      {/* Team */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.0 }}
      >
        <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
          Team
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {team.map((member, index) => (
            <Card key={index} padding="lg" className="text-center" hover animate>
              <div className="text-4xl mb-4">{member.avatar}</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                {member.name}
              </h3>
              <p className="text-primary-600 text-sm font-medium mb-3">
                {member.role}
              </p>
              <p className="text-gray-600 text-sm">
                {member.description}
              </p>
            </Card>
          ))}
        </div>
      </motion.div>

      {/* Contributing */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.2 }}
      >
        <Card padding="lg">
          <div className="text-center">
            <Code className="w-12 h-12 text-primary-600 mx-auto mb-4" />
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Contributing
            </h2>
            <p className="text-lg text-gray-700 mb-8 max-w-3xl mx-auto">
              Suplementor is an open-source project and we welcome contributions from the community. 
              Whether you're a developer, researcher, or domain expert, there are many ways to help 
              improve the platform.
            </p>
            <div className="flex flex-wrap items-center justify-center gap-4">
              <Button
                variant="primary"
                leftIcon={<Github className="w-4 h-4" />}
                rightIcon={<ExternalLink className="w-4 h-4" />}
              >
                View on GitHub
              </Button>
              <Button
                variant="outline"
                leftIcon={<BookOpen className="w-4 h-4" />}
              >
                Documentation
              </Button>
              <Button
                variant="outline"
                leftIcon={<Users className="w-4 h-4" />}
              >
                Join Community
              </Button>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Contact */}
      <motion.div
        className="mb-16"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.4 }}
      >
        <Card padding="lg" className="bg-gray-50">
          <div className="text-center">
            <Mail className="w-12 h-12 text-primary-600 mx-auto mb-4" />
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Get in Touch
            </h2>
            <p className="text-lg text-gray-700 mb-8">
              Have questions, suggestions, or want to collaborate? We'd love to hear from you!
            </p>
            <div className="flex flex-wrap items-center justify-center gap-4">
              <Button
                variant="primary"
                leftIcon={<Mail className="w-4 h-4" />}
              >
                Contact Us
              </Button>
              <Button
                variant="outline"
                leftIcon={<Github className="w-4 h-4" />}
              >
                Report Issue
              </Button>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Footer */}
      <motion.div
        className="text-center text-gray-500 text-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 1.6 }}
      >
        <p className="mb-2">
          Built with ❤️ by the Suplementor team
        </p>
        <p>
          © 2024 Suplementor. Released under the MIT License.
        </p>
        <div className="flex items-center justify-center space-x-6 mt-4">
          <a href="#" className="hover:text-primary-600 transition-colors">
            Privacy Policy
          </a>
          <a href="#" className="hover:text-primary-600 transition-colors">
            Terms of Service
          </a>
          <a href="#" className="hover:text-primary-600 transition-colors">
            License
          </a>
        </div>
      </motion.div>
    </div>
  );
};

export default AboutPage;
