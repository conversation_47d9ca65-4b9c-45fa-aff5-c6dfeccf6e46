import React, { useState, useCallback, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import { useDebounce } from 'use-debounce';
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Grid,
  List,
  Download,
  Eye,
  ExternalLink,
  Clock,
  Tag,
  Star,
} from 'lucide-react';

import Input from '@/components/atoms/Input';
import Button from '@/components/atoms/Button';
import Card from '@/components/atoms/Card';
import Badge from '@/components/atoms/Badge';
import LoadingSpinner from '@/components/atoms/LoadingSpinner';

import { SearchResult, SearchFilters, SortConfig } from '@/types';
import { searchService } from '@/services/searchService';

const SearchPage: React.FC = () => {
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery] = useDebounce(searchQuery, 300);
  const [filters, setFilters] = useState<SearchFilters>({
    types: [],
    sources: [],
  });
  const [sortConfig, setSortConfig] = useState<SortConfig<SearchResult>>({
    field: 'score',
    direction: 'desc',
  });
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedResults, setSelectedResults] = useState<Set<string>>(new Set());

  // Fetch search results
  const {
    data: searchResults,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['search', debouncedQuery, filters, sortConfig],
    queryFn: () => searchService.search(debouncedQuery, {
      ...filters,
      sortBy: sortConfig.field,
      sortDirection: sortConfig.direction,
    }),
    enabled: debouncedQuery.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Fetch search suggestions
  const { data: suggestions } = useQuery({
    queryKey: ['search-suggestions', debouncedQuery],
    queryFn: () => searchService.getSuggestions(debouncedQuery),
    enabled: debouncedQuery.length > 2 && debouncedQuery.length < 20,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Handle sorting
  const handleSort = useCallback((field: keyof SearchResult) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  }, []);

  // Handle result selection
  const handleResultSelect = useCallback((resultId: string, selected: boolean) => {
    setSelectedResults(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(resultId);
      } else {
        newSet.delete(resultId);
      }
      return newSet;
    });
  }, []);

  // Handle bulk actions
  const handleBulkExport = useCallback(() => {
    if (selectedResults.size === 0) return;
    
    const selectedData = searchResults?.filter(result => 
      selectedResults.has(result.id)
    );
    
    if (selectedData) {
      const dataStr = JSON.stringify(selectedData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'search-results.json';
      link.click();
      URL.revokeObjectURL(url);
    }
  }, [selectedResults, searchResults]);

  // Memoized filtered and sorted results
  const processedResults = useMemo(() => {
    if (!searchResults) return [];
    
    return [...searchResults].sort((a, b) => {
      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortConfig.direction === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' 
          ? aValue - bValue 
          : bValue - aValue;
      }
      
      return 0;
    });
  }, [searchResults, sortConfig]);

  // Get available filter options
  const availableTypes = useMemo(() => {
    if (!searchResults) return [];
    const types = new Set(searchResults.map(result => result.type));
    return Array.from(types);
  }, [searchResults]);

  const availableSources = useMemo(() => {
    if (!searchResults) return [];
    const sources = new Set(
      searchResults
        .map(result => result.metadata?.source)
        .filter(Boolean)
    );
    return Array.from(sources);
  }, [searchResults]);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Search Knowledge Base
        </h1>
        <p className="text-gray-600">
          Search through supplements, ingredients, studies, and more
        </p>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <Input
            placeholder="Search for supplements, ingredients, studies..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            leftIcon={<Search className="w-5 h-5" />}
            className="text-lg py-3"
            fullWidth
          />
          
          {/* Search Suggestions */}
          {suggestions && suggestions.length > 0 && searchQuery.length > 2 && (
            <motion.div
              className="absolute top-full left-0 right-0 z-40 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
            >
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  className="w-full px-4 py-2 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                  onClick={() => handleSearch(suggestion)}
                >
                  <div className="flex items-center">
                    <Search className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-gray-900">{suggestion}</span>
                  </div>
                </button>
              ))}
            </motion.div>
          )}
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          {/* Filters */}
          <Button
            variant={showFilters ? 'primary' : 'outline'}
            onClick={() => setShowFilters(!showFilters)}
            leftIcon={<Filter className="w-4 h-4" />}
          >
            Filters
          </Button>

          {/* View Mode */}
          <div className="flex items-center border border-gray-300 rounded-md">
            <Button
              variant={viewMode === 'list' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-r-none border-r"
            >
              <List className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'grid' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-l-none"
            >
              <Grid className="w-4 h-4" />
            </Button>
          </div>

          {/* Sort */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSort('score')}
              leftIcon={
                sortConfig.field === 'score' ? (
                  sortConfig.direction === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />
                ) : null
              }
            >
              Relevance
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSort('name')}
              leftIcon={
                sortConfig.field === 'name' ? (
                  sortConfig.direction === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />
                ) : null
              }
            >
              Name
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Results count */}
          {searchResults && (
            <span className="text-sm text-gray-600">
              {searchResults.length} results
            </span>
          )}

          {/* Bulk actions */}
          {selectedResults.size > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleBulkExport}
              leftIcon={<Download className="w-4 h-4" />}
            >
              Export ({selectedResults.size})
            </Button>
          )}
        </div>
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            className="mb-6 p-4 bg-gray-50 rounded-lg"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Type filters */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content Type
                </label>
                <div className="space-y-2">
                  {availableTypes.map(type => (
                    <label key={type} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.types?.includes(type) || false}
                        onChange={(e) => {
                          const types = filters.types || [];
                          if (e.target.checked) {
                            handleFilterChange({ types: [...types, type] });
                          } else {
                            handleFilterChange({ types: types.filter(t => t !== type) });
                          }
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700 capitalize">{type}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Source filters */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Source
                </label>
                <div className="space-y-2">
                  {availableSources.map(source => (
                    <label key={source} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.sources?.includes(source) || false}
                        onChange={(e) => {
                          const sources = filters.sources || [];
                          if (e.target.checked) {
                            handleFilterChange({ sources: [...sources, source] });
                          } else {
                            handleFilterChange({ sources: sources.filter(s => s !== source) });
                          }
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">{source}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Date range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date Range
                </label>
                <div className="space-y-2">
                  <Input
                    type="date"
                    placeholder="Start date"
                    value={filters.dateRange?.start || ''}
                    onChange={(e) => handleFilterChange({
                      dateRange: { ...filters.dateRange, start: e.target.value }
                    })}
                    size="sm"
                  />
                  <Input
                    type="date"
                    placeholder="End date"
                    value={filters.dateRange?.end || ''}
                    onChange={(e) => handleFilterChange({
                      dateRange: { ...filters.dateRange, end: e.target.value }
                    })}
                    size="sm"
                  />
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Results */}
      <div className="space-y-6">
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" text="Searching..." />
          </div>
        )}

        {error && (
          <Card padding="lg" className="text-center">
            <div className="text-red-500 mb-4">
              <Search className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Search Failed
            </h3>
            <p className="text-gray-600 mb-4">
              There was an error performing your search. Please try again.
            </p>
            <Button onClick={() => refetch()}>
              Try Again
            </Button>
          </Card>
        )}

        {searchQuery && !isLoading && !error && processedResults.length === 0 && (
          <Card padding="lg" className="text-center">
            <div className="text-gray-400 mb-4">
              <Search className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No Results Found
            </h3>
            <p className="text-gray-600 mb-4">
              No results found for "{searchQuery}". Try adjusting your search terms or filters.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchQuery('');
                setFilters({ types: [], sources: [] });
              }}
            >
              Clear Search
            </Button>
          </Card>
        )}

        {processedResults.length > 0 && (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {processedResults.map((result) => (
              <motion.div
                key={result.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Card
                  padding="lg"
                  hover
                  clickable
                  className={`${selectedResults.has(result.id) ? 'ring-2 ring-primary-500' : ''}`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedResults.has(result.id)}
                        onChange={(e) => handleResultSelect(result.id, e.target.checked)}
                        className="rounded"
                      />
                      <Badge variant="primary" size="sm">
                        {result.type}
                      </Badge>
                      {result.score && (
                        <div className="flex items-center text-xs text-gray-500">
                          <Star className="w-3 h-3 mr-1" />
                          {Math.round(result.score * 100)}%
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      <Button variant="ghost" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {result.name}
                  </h3>

                  {result.description && (
                    <p className="text-gray-600 mb-3 line-clamp-3">
                      {result.description}
                    </p>
                  )}

                  {result.highlights && result.highlights.length > 0 && (
                    <div className="mb-3">
                      <p className="text-xs font-medium text-gray-500 mb-1">Highlights:</p>
                      <div className="space-y-1">
                        {result.highlights.slice(0, 2).map((highlight, index) => (
                          <p key={index} className="text-sm text-gray-700 bg-yellow-50 px-2 py-1 rounded">
                            ...{highlight}...
                          </p>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-4">
                      {result.metadata?.source && (
                        <div className="flex items-center">
                          <Tag className="w-3 h-3 mr-1" />
                          {result.metadata.source}
                        </div>
                      )}
                      {result.metadata?.createdAt && (
                        <div className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {new Date(result.metadata.createdAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchPage;
