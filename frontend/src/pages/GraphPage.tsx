import React, { useState, useEffect, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Filter,
  Search,
  Plus,
  Download,
  Share2,
  Zap,
  Eye,
  EyeOff,
  Sliders,
} from 'lucide-react';

import GraphVisualization from '@/components/organisms/GraphVisualization';
import Button from '@/components/atoms/Button';
import Input from '@/components/atoms/Input';
import Card from '@/components/atoms/Card';
import Badge from '@/components/atoms/Badge';
import LoadingSpinner from '@/components/atoms/LoadingSpinner';

import { GraphData, GraphNode, GraphRelationship, GraphFilters } from '@/types';
import { graphService } from '@/services/graphService';

const GraphPage: React.FC = () => {
  // State management
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [selectedRelationship, setSelectedRelationship] = useState<GraphRelationship | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<GraphFilters>({
    nodeTypes: [],
    relationshipTypes: [],
    limit: 1000,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [showLabels, setShowLabels] = useState(true);
  const [showRelationshipLabels, setShowRelationshipLabels] = useState(false);
  const [graphSettings, setGraphSettings] = useState({
    nodeSize: 8,
    linkDistance: 100,
    chargeStrength: -300,
  });

  // Fetch graph data
  const {
    data: graphData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['graph-data', filters, searchQuery],
    queryFn: () => graphService.getGraphData({ ...filters, search: searchQuery }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch graph statistics
  const { data: graphStats } = useQuery({
    queryKey: ['graph-stats'],
    queryFn: () => graphService.getGraphStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Handle node selection
  const handleNodeClick = useCallback((node: GraphNode, event: MouseEvent) => {
    setSelectedNode(node);
    setSelectedRelationship(null);
  }, []);

  // Handle relationship selection
  const handleRelationshipClick = useCallback((relationship: GraphRelationship, event: MouseEvent) => {
    setSelectedRelationship(relationship);
    setSelectedNode(null);
  }, []);

  // Handle background click
  const handleBackgroundClick = useCallback((event: MouseEvent) => {
    setSelectedNode(null);
    setSelectedRelationship(null);
  }, []);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<GraphFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Handle node expansion
  const handleExpandNode = useCallback(async (nodeId: string) => {
    try {
      await graphService.expandGraph(nodeId, { expansionType: 'related', limit: 10 });
      refetch();
    } catch (error) {
      console.error('Failed to expand node:', error);
    }
  }, [refetch]);

  // Export graph data
  const handleExport = useCallback(() => {
    if (!graphData) return;
    
    const dataStr = JSON.stringify(graphData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'graph-data.json';
    link.click();
    URL.revokeObjectURL(url);
  }, [graphData]);

  // Share graph
  const handleShare = useCallback(() => {
    if (navigator.share) {
      navigator.share({
        title: 'Suplementor Knowledge Graph',
        text: 'Check out this interactive supplement knowledge graph!',
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" text="Loading knowledge graph..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Card padding="lg" className="text-center max-w-md">
          <div className="text-red-500 mb-4">
            <Zap className="w-12 h-12 mx-auto" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Failed to load graph
          </h2>
          <p className="text-gray-600 mb-4">
            There was an error loading the knowledge graph. Please try again.
          </p>
          <Button onClick={() => refetch()}>
            Try Again
          </Button>
        </Card>
      </div>
    );
  }

  const data: GraphData = graphData || { nodes: [], relationships: [] };

  return (
    <div className="h-screen flex flex-col">
      {/* Top toolbar */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="relative">
              <Input
                placeholder="Search nodes..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                leftIcon={<Search className="w-4 h-4" />}
                className="w-64"
              />
            </div>

            {/* Filters */}
            <Button
              variant={showFilters ? 'primary' : 'outline'}
              onClick={() => setShowFilters(!showFilters)}
              leftIcon={<Filter className="w-4 h-4" />}
            >
              Filters
            </Button>

            {/* View options */}
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowLabels(!showLabels)}
                title="Toggle node labels"
              >
                {showLabels ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowRelationshipLabels(!showRelationshipLabels)}
                title="Toggle relationship labels"
              >
                <Sliders className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Graph stats */}
            {graphStats && (
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <Badge variant="secondary">
                  {graphStats.totalNodes} nodes
                </Badge>
                <Badge variant="secondary">
                  {graphStats.totalRelationships} relationships
                </Badge>
              </div>
            )}

            {/* Actions */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              leftIcon={<Download className="w-4 h-4" />}
            >
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              leftIcon={<Share2 className="w-4 h-4" />}
            >
              Share
            </Button>
            <Button
              variant="primary"
              size="sm"
              leftIcon={<Plus className="w-4 h-4" />}
            >
              Add Node
            </Button>
          </div>
        </div>

        {/* Filters panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              className="mt-4 p-4 bg-gray-50 rounded-lg"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Node Types
                  </label>
                  <div className="space-y-2">
                    {['Supplement', 'Ingredient', 'Effect', 'Study', 'Condition'].map(type => (
                      <label key={type} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.nodeTypes?.includes(type) || false}
                          onChange={(e) => {
                            const nodeTypes = filters.nodeTypes || [];
                            if (e.target.checked) {
                              handleFilterChange({ nodeTypes: [...nodeTypes, type] });
                            } else {
                              handleFilterChange({ nodeTypes: nodeTypes.filter(t => t !== type) });
                            }
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">{type}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Relationship Types
                  </label>
                  <div className="space-y-2">
                    {['CONTAINS', 'CAUSES', 'INTERACTS_WITH', 'STUDIED_IN', 'TREATS'].map(type => (
                      <label key={type} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.relationshipTypes?.includes(type) || false}
                          onChange={(e) => {
                            const relationshipTypes = filters.relationshipTypes || [];
                            if (e.target.checked) {
                              handleFilterChange({ relationshipTypes: [...relationshipTypes, type] });
                            } else {
                              handleFilterChange({ relationshipTypes: relationshipTypes.filter(t => t !== type) });
                            }
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">{type}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Limit
                  </label>
                  <Input
                    type="number"
                    value={filters.limit || 1000}
                    onChange={(e) => handleFilterChange({ limit: parseInt(e.target.value) })}
                    min={1}
                    max={10000}
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Main content */}
      <div className="flex-1 flex">
        {/* Graph visualization */}
        <div className="flex-1 relative">
          <GraphVisualization
            data={data}
            width={window.innerWidth - (selectedNode || selectedRelationship ? 400 : 0)}
            height={window.innerHeight - 120}
            onNodeClick={handleNodeClick}
            onRelationshipClick={handleRelationshipClick}
            onBackgroundClick={handleBackgroundClick}
            showLabels={showLabels}
            showRelationshipLabels={showRelationshipLabels}
            nodeSize={graphSettings.nodeSize}
            linkDistance={graphSettings.linkDistance}
            chargeStrength={graphSettings.chargeStrength}
          />
        </div>

        {/* Side panel */}
        <AnimatePresence>
          {(selectedNode || selectedRelationship) && (
            <motion.div
              className="w-96 bg-white border-l border-gray-200 overflow-y-auto"
              initial={{ x: 400 }}
              animate={{ x: 0 }}
              exit={{ x: 400 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            >
              <div className="p-6">
                {selectedNode && (
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-xl font-semibold text-gray-900">
                        Node Details
                      </h2>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedNode(null)}
                      >
                        ×
                      </Button>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {selectedNode.properties.name}
                        </h3>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {selectedNode.labels.map(label => (
                            <Badge key={label} variant="primary" size="sm">
                              {label}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {selectedNode.properties.description && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-1">
                            Description
                          </h4>
                          <p className="text-sm text-gray-600">
                            {selectedNode.properties.description}
                          </p>
                        </div>
                      )}

                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">
                          Properties
                        </h4>
                        <div className="space-y-2">
                          {Object.entries(selectedNode.properties).map(([key, value]) => (
                            key !== 'name' && key !== 'description' && (
                              <div key={key} className="flex justify-between text-sm">
                                <span className="text-gray-500 capitalize">
                                  {key.replace(/([A-Z])/g, ' $1').trim()}:
                                </span>
                                <span className="text-gray-900 font-medium">
                                  {String(value)}
                                </span>
                              </div>
                            )
                          ))}
                        </div>
                      </div>

                      <div className="pt-4 border-t border-gray-200">
                        <Button
                          variant="primary"
                          size="sm"
                          fullWidth
                          onClick={() => handleExpandNode(selectedNode.id)}
                          leftIcon={<Plus className="w-4 h-4" />}
                        >
                          Expand Node
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {selectedRelationship && (
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-xl font-semibold text-gray-900">
                        Relationship Details
                      </h2>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedRelationship(null)}
                      >
                        ×
                      </Button>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {selectedRelationship.type}
                        </h3>
                        <Badge variant="secondary" size="sm" className="mt-2">
                          Relationship
                        </Badge>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">
                          Properties
                        </h4>
                        <div className="space-y-2">
                          {Object.entries(selectedRelationship.properties).map(([key, value]) => (
                            <div key={key} className="flex justify-between text-sm">
                              <span className="text-gray-500 capitalize">
                                {key.replace(/([A-Z])/g, ' $1').trim()}:
                              </span>
                              <span className="text-gray-900 font-medium">
                                {String(value)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default GraphPage;
