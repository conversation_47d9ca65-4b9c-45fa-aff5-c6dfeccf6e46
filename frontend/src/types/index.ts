// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    message: string;
    code?: string;
    statusCode: number;
    timestamp: string;
    path: string;
    method: string;
    stack?: string;
    details?: any;
  };
  timestamp: string;
}

// Graph Types
export interface GraphNode {
  id: string;
  labels: string[];
  properties: {
    id?: string;
    name: string;
    description?: string;
    [key: string]: any;
  };
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
  vx?: number;
  vy?: number;
}

export interface GraphRelationship {
  id: string;
  type: string;
  properties: {
    [key: string]: any;
  };
  source: string | GraphNode;
  target: string | GraphNode;
}

export interface GraphData {
  nodes: GraphNode[];
  relationships: GraphRelationship[];
}

export interface GraphFilters {
  nodeTypes?: string[];
  relationshipTypes?: string[];
  limit?: number;
  search?: string;
}

// Supplement Types
export interface Supplement {
  id: string;
  name: string;
  description?: string;
  brand?: string;
  category?: string;
  dosage?: string;
  ingredients?: string[];
  benefits?: string[];
  sideEffects?: string[];
  contraindications?: string[];
  sourceUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Ingredient {
  id: string;
  name: string;
  chemicalName?: string;
  description?: string;
  molecularFormula?: string;
  casNumber?: string;
  functions?: string[];
  sources?: string[];
  safetyProfile?: string;
  interactions?: string[];
}

export interface Effect {
  id: string;
  name: string;
  description?: string;
  category?: string;
  mechanism?: string;
  evidenceLevel?: string;
  timeToEffect?: string;
  duration?: string;
}

export interface Study {
  id: string;
  title: string;
  abstract?: string;
  authors?: string[];
  journal?: string;
  publicationDate?: string;
  doi?: string;
  pmid?: string;
  studyType?: string;
  participants?: number;
  conclusions?: string;
  qualityScore?: number;
}

// AI Types
export interface AIAnalysis {
  analysis: string;
  type: string;
  timestamp: string;
  includeConfidence?: boolean;
  keyFindings?: string[];
  entities?: ExtractedEntity[];
  confidence?: number;
}

export interface ExtractedEntity {
  name: string;
  type: string;
  description?: string;
  confidence: number;
}

export interface ExtractedRelationship {
  source: string;
  target: string;
  type: string;
  confidence: number;
}

export interface ExtractionResult {
  entities: ExtractedEntity[];
  relationships: ExtractedRelationship[];
}

export interface Recommendation {
  supplement: string;
  reason: string;
  dosage?: string;
  benefits?: string[];
  safetyConsiderations?: string[];
  evidenceLevel?: string;
  confidence: number;
}

export interface Interaction {
  supplements: string[];
  type: 'synergistic' | 'antagonistic' | 'dangerous' | 'neutral';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  recommendations?: string[];
  evidenceLevel?: string;
}

// RAG Types
export interface RAGQuery {
  question: string;
  results: any[];
  context: RAGContext[];
  totalResults: number;
  timestamp: string;
}

export interface RAGContext {
  content: string;
  source?: string;
  certainty?: number;
  metadata?: any;
}

export interface RAGDocument {
  id: string;
  content: string;
  title?: string;
  source?: string;
  metadata?: any;
  className?: string;
  createdAt: string;
  updatedAt: string;
}

// Upload Types
export interface UploadedFile {
  id: string;
  originalName: string;
  fileName: string;
  filePath?: string;
  mimeType: string;
  size: number;
  extractedText?: string;
  metadata?: any;
  source?: string;
  processed: boolean;
  addedToKnowledgeBase: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UploadOptions {
  extractText?: boolean;
  addToKnowledgeBase?: boolean;
  source?: string;
  metadata?: any;
}

// Search Types
export interface SearchResult {
  id: string;
  type: 'supplement' | 'ingredient' | 'effect' | 'study' | 'document';
  name: string;
  description?: string;
  score?: number;
  highlights?: string[];
  metadata?: any;
}

export interface SearchFilters {
  types?: string[];
  sources?: string[];
  dateRange?: {
    start?: string;
    end?: string;
  };
  confidence?: {
    min?: number;
    max?: number;
  };
}

// UI State Types
export interface ViewState {
  selectedNode?: GraphNode;
  selectedRelationship?: GraphRelationship;
  hoveredNode?: GraphNode;
  hoveredRelationship?: GraphRelationship;
  searchQuery: string;
  filters: GraphFilters;
  sidebarOpen: boolean;
  modalOpen: boolean;
  loading: boolean;
  error?: string;
}

export interface GraphViewState {
  zoom: number;
  pan: { x: number; y: number };
  selectedNodes: Set<string>;
  highlightedNodes: Set<string>;
  showLabels: boolean;
  showRelationshipLabels: boolean;
  nodeSize: number;
  linkDistance: number;
  chargeStrength: number;
}

// Pagination Types
export interface PaginationInfo {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationInfo;
  timestamp: string;
}

// Statistics Types
export interface GraphStats {
  totalNodes: number;
  totalRelationships: number;
  nodesByType: Array<{ label: string; count: number }>;
  relationshipsByType: Array<{ type: string; count: number }>;
  lastUpdated: string;
}

export interface SystemStats {
  uptime: number;
  memory: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  cpu: {
    user: number;
    system: number;
  };
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'number' | 'date' | 'file';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

export interface FormData {
  [key: string]: any;
}

// Theme Types
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    background: string;
    surface: string;
    text: string;
  };
  fonts: {
    sans: string;
    mono: string;
  };
}

// Configuration Types
export interface AppConfig {
  apiUrl: string;
  appName: string;
  version: string;
  features: {
    aiEnabled: boolean;
    ragEnabled: boolean;
    uploadEnabled: boolean;
    graphExpansion: boolean;
  };
  limits: {
    maxFileSize: number;
    maxNodes: number;
    maxRelationships: number;
  };
}

// Event Types
export interface GraphEvent {
  type: 'node-click' | 'node-hover' | 'relationship-click' | 'relationship-hover' | 'background-click';
  data?: GraphNode | GraphRelationship;
  position?: { x: number; y: number };
  originalEvent?: Event;
}

export interface AppEvent {
  type: string;
  payload?: any;
  timestamp: number;
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type ID = string | number;

export type Status = 'idle' | 'loading' | 'success' | 'error';

export type SortDirection = 'asc' | 'desc';

export type SortField<T> = keyof T;

export interface SortConfig<T> {
  field: SortField<T>;
  direction: SortDirection;
}
