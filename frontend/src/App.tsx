import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

// Layout components
import Layout from '@/components/layout/Layout';
import LoadingSpinner from '@/components/atoms/LoadingSpinner';
import ErrorBoundary from '@/components/atoms/ErrorBoundary';

// Lazy load pages for better performance
const GraphPage = React.lazy(() => import('@/pages/GraphPage'));
const SearchPage = React.lazy(() => import('@/pages/SearchPage'));
const ResearchPage = React.lazy(() => import('@/pages/ResearchPage'));
const UploadPage = React.lazy(() => import('@/pages/UploadPage'));
const AnalyticsPage = React.lazy(() => import('@/pages/AnalyticsPage'));
const SettingsPage = React.lazy(() => import('@/pages/SettingsPage'));
const AboutPage = React.lazy(() => import('@/pages/AboutPage'));

// Loading component for Suspense
const PageLoader: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <LoadingSpinner size="lg" />
      <p className="mt-4 text-gray-600">Loading page...</p>
    </div>
  </div>
);

// Page transition variants
const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  in: {
    opacity: 1,
    y: 0,
  },
  out: {
    opacity: 0,
    y: -20,
  },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3,
};

// Route wrapper with animations
const AnimatedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <motion.div
    initial="initial"
    animate="in"
    exit="out"
    variants={pageVariants}
    transition={pageTransition}
    className="w-full h-full"
  >
    {children}
  </motion.div>
);

const App: React.FC = () => {
  return (
    <div className="App">
      <ErrorBoundary>
        <Layout>
          <AnimatePresence mode="wait">
            <Suspense fallback={<PageLoader />}>
              <Routes>
                {/* Default route - redirect to graph */}
                <Route path="/" element={<Navigate to="/graph" replace />} />
                
                {/* Main application routes */}
                <Route
                  path="/graph"
                  element={
                    <AnimatedRoute>
                      <GraphPage />
                    </AnimatedRoute>
                  }
                />
                
                <Route
                  path="/search"
                  element={
                    <AnimatedRoute>
                      <SearchPage />
                    </AnimatedRoute>
                  }
                />

                <Route
                  path="/research"
                  element={
                    <AnimatedRoute>
                      <ResearchPage />
                    </AnimatedRoute>
                  }
                />

                <Route
                  path="/upload"
                  element={
                    <AnimatedRoute>
                      <UploadPage />
                    </AnimatedRoute>
                  }
                />
                
                <Route
                  path="/analytics"
                  element={
                    <AnimatedRoute>
                      <AnalyticsPage />
                    </AnimatedRoute>
                  }
                />
                
                <Route
                  path="/settings"
                  element={
                    <AnimatedRoute>
                      <SettingsPage />
                    </AnimatedRoute>
                  }
                />
                
                <Route
                  path="/about"
                  element={
                    <AnimatedRoute>
                      <AboutPage />
                    </AnimatedRoute>
                  }
                />
                
                {/* Catch-all route for 404 */}
                <Route
                  path="*"
                  element={
                    <AnimatedRoute>
                      <div className="flex items-center justify-center min-h-screen">
                        <div className="text-center">
                          <div className="mb-4">
                            <svg
                              className="mx-auto h-12 w-12 text-gray-400"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                              aria-hidden="true"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.467-.881-6.08-2.33"
                              />
                            </svg>
                          </div>
                          <h1 className="text-4xl font-bold text-gray-900 mb-2">404</h1>
                          <h2 className="text-xl font-semibold text-gray-700 mb-4">
                            Page Not Found
                          </h2>
                          <p className="text-gray-600 mb-6 max-w-md">
                            The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
                          </p>
                          <div className="space-x-4">
                            <button
                              onClick={() => window.history.back()}
                              className="btn btn-secondary"
                            >
                              Go Back
                            </button>
                            <button
                              onClick={() => window.location.href = '/graph'}
                              className="btn btn-primary"
                            >
                              Go to Graph
                            </button>
                          </div>
                        </div>
                      </div>
                    </AnimatedRoute>
                  }
                />
              </Routes>
            </Suspense>
          </AnimatePresence>
        </Layout>
      </ErrorBoundary>
    </div>
  );
};

export default App;
