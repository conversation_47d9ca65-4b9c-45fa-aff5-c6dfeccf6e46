{"name": "suplementor-frontend", "version": "1.0.0", "description": "Frontend for Suplementor Knowledge Graph", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,json}\"", "type-check": "tsc --noEmit", "docker:build": "docker build -t suplementor-frontend .", "docker:run": "docker run -p 5173:5173 suplementor-frontend"}, "keywords": ["knowledge-graph", "supplements", "react", "typescript", "d3", "visualization", "ai", "rag"], "author": "Suplementor Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "d3": "^7.8.5", "d3-force": "^3.0.0", "d3-selection": "^3.0.0", "d3-zoom": "^3.0.0", "d3-drag": "^3.0.0", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.0.0", "axios": "^1.6.2", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "react-syntax-highlighter": "^15.5.0", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "react-intersection-observer": "^9.5.3", "use-debounce": "^10.0.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "uuid": "^9.0.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/d3": "^7.4.3", "@types/d3-force": "^3.0.9", "@types/d3-selection": "^3.0.10", "@types/d3-zoom": "^3.0.8", "@types/d3-drag": "^3.0.7", "@types/d3-scale": "^4.0.8", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "c8": "^8.0.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "typescript": "^5.2.2", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"root": true, "env": {"browser": true, "es2020": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "eslint-plugin-react-hooks/recommended"], "ignorePatterns": ["dist", ".eslintrc.cjs"], "parser": "@typescript-eslint/parser", "plugins": ["react-refresh"], "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/explicit-function-return-type": "warn", "no-console": "warn"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false}}