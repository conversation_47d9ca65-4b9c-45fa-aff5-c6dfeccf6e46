#!/bin/bash

# Suplementor Knowledge Graph - Setup Script
# This script sets up the development environment

set -e

echo "🔧 Setting up Suplementor Knowledge Graph development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
print_status "Checking prerequisites..."

# Check Node.js
if ! command -v node > /dev/null 2>&1; then
    print_error "Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

print_success "Node.js $(node -v) is installed"

# Check npm
if ! command -v npm > /dev/null 2>&1; then
    print_error "npm is not installed. Please install npm and try again."
    exit 1
fi

print_success "npm $(npm -v) is installed"

# Check Docker
if ! command -v docker > /dev/null 2>&1; then
    print_error "Docker is not installed. Please install Docker and try again."
    exit 1
fi

print_success "Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1) is installed"

# Check Docker Compose
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

print_success "Docker Compose $(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1) is installed"

# Create necessary directories
print_status "Creating project directories..."
mkdir -p backend/uploads
mkdir -p backend/logs
mkdir -p frontend/dist
mkdir -p data/neo4j
mkdir -p data/weaviate
mkdir -p data/redis
mkdir -p data/mongodb
mkdir -p monitoring/prometheus
mkdir -p monitoring/grafana

print_success "Directories created"

# Setup environment files
print_status "Setting up environment files..."

if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    print_success "Created backend/.env from example"
    print_warning "Please edit backend/.env with your API keys and configuration"
else
    print_status "backend/.env already exists"
fi

if [ ! -f frontend/.env ]; then
    cp frontend/.env.example frontend/.env
    print_success "Created frontend/.env from example"
else
    print_status "frontend/.env already exists"
fi

# Install backend dependencies
print_status "Installing backend dependencies..."
cd backend
npm install
print_success "Backend dependencies installed"

# Install frontend dependencies
print_status "Installing frontend dependencies..."
cd ../frontend
npm install
print_success "Frontend dependencies installed"

cd ..

# Make scripts executable
print_status "Making scripts executable..."
chmod +x scripts/*.sh
print_success "Scripts are now executable"

# Setup Git hooks (if .git exists)
if [ -d ".git" ]; then
    print_status "Setting up Git hooks..."
    
    # Pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "Running pre-commit checks..."

# Check if backend code passes linting
cd backend
npm run lint
if [ $? -ne 0 ]; then
    echo "Backend linting failed. Please fix the issues and try again."
    exit 1
fi

# Check if frontend code passes linting
cd ../frontend
npm run lint
if [ $? -ne 0 ]; then
    echo "Frontend linting failed. Please fix the issues and try again."
    exit 1
fi

echo "Pre-commit checks passed!"
EOF

    chmod +x .git/hooks/pre-commit
    print_success "Git hooks configured"
fi

# Create sample data (optional)
read -p "Do you want to create sample data? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Creating sample data..."
    # This would run a script to populate the databases with sample data
    # For now, we'll just create placeholder files
    echo '{"message": "Sample supplement data"}' > backend/data/sample-supplements.json
    echo '{"message": "Sample ingredient data"}' > backend/data/sample-ingredients.json
    print_success "Sample data created"
fi

# Final instructions
echo ""
print_success "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "  1. Edit backend/.env with your API keys"
echo "  2. Run './scripts/start.sh' to start all services"
echo "  3. Visit http://localhost:5173 to see the application"
echo ""
echo "🔧 Development commands:"
echo "  ./scripts/start.sh     # Start all services"
echo "  ./scripts/stop.sh      # Stop all services"
echo "  npm run dev            # Start individual services in dev mode"
echo ""
echo "📚 Documentation:"
echo "  README.md              # Main documentation"
echo "  backend/README.md      # Backend-specific docs"
echo "  frontend/README.md     # Frontend-specific docs"
echo ""

# Check if API keys need to be configured
if grep -q "your_.*_api_key" backend/.env 2>/dev/null; then
    print_warning "⚠️  Don't forget to configure your API keys in backend/.env!"
    echo "   Required: GEMINI_API_KEY, OPENAI_API_KEY (optional)"
fi

print_success "Happy coding! 🚀"
