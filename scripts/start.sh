#!/bin/bash

# Suplementor Knowledge Graph - Start Script
# This script starts the entire Suplementor stack

set -e

echo "🚀 Starting Suplementor Knowledge Graph..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p backend/uploads
mkdir -p backend/logs
mkdir -p data/neo4j
mkdir -p data/weaviate
mkdir -p data/redis
mkdir -p data/mongodb

# Copy environment files if they don't exist
if [ ! -f backend/.env ]; then
    print_status "Creating backend .env file from example..."
    cp backend/.env.example backend/.env
    print_warning "Please edit backend/.env with your API keys and configuration"
fi

if [ ! -f frontend/.env ]; then
    print_status "Creating frontend .env file from example..."
    cp frontend/.env.example frontend/.env
fi

# Start the services
print_status "Starting Docker services..."
docker-compose up -d

# Wait for services to be healthy
print_status "Waiting for services to be ready..."

# Function to wait for service
wait_for_service() {
    local service=$1
    local url=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            print_success "$service is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service failed to start within expected time"
    return 1
}

# Wait for each service
wait_for_service "Neo4j" "http://localhost:7474"
wait_for_service "Weaviate" "http://localhost:8080/v1/.well-known/ready"
wait_for_service "Backend API" "http://localhost:3000/api/health"
wait_for_service "Frontend" "http://localhost:5173"

print_success "All services are ready!"

# Display service URLs
echo ""
echo "🎉 Suplementor Knowledge Graph is now running!"
echo ""
echo "📱 Frontend:          http://localhost:5173"
echo "🔧 Backend API:       http://localhost:3000"
echo "📊 Neo4j Browser:     http://localhost:7474 (neo4j/password)"
echo "🔍 Weaviate:          http://localhost:8080"
echo "⚡ Redis:             localhost:6379"
echo "📄 MongoDB:           localhost:27017"
echo ""
echo "📋 Useful commands:"
echo "  docker-compose logs -f backend    # View backend logs"
echo "  docker-compose logs -f frontend   # View frontend logs"
echo "  docker-compose ps                 # Check service status"
echo "  docker-compose down               # Stop all services"
echo ""
echo "🔧 Configuration:"
echo "  Edit backend/.env for API keys and database settings"
echo "  Edit frontend/.env for frontend configuration"
echo ""

# Check if API keys are configured
if grep -q "your_.*_api_key" backend/.env 2>/dev/null; then
    print_warning "Remember to configure your API keys in backend/.env for full functionality!"
fi

print_success "Setup complete! Happy coding! 🚀"
