#!/bin/bash

# Suplementor Knowledge Graph - Stop Script
# This script stops the entire Suplementor stack

set -e

echo "🛑 Stopping Suplementor Knowledge Graph..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "Docker Compose is not installed."
    exit 1
fi

# Stop the services
print_status "Stopping Docker services..."
docker-compose down

print_success "All services stopped!"

# Option to remove volumes
read -p "Do you want to remove all data volumes? This will delete all data! (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Removing all data volumes..."
    docker-compose down -v
    print_success "All volumes removed!"
else
    print_status "Data volumes preserved."
fi

# Option to remove images
read -p "Do you want to remove Docker images? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Removing Docker images..."
    docker-compose down --rmi all
    print_success "All images removed!"
fi

print_success "Suplementor Knowledge Graph stopped successfully! 👋"
