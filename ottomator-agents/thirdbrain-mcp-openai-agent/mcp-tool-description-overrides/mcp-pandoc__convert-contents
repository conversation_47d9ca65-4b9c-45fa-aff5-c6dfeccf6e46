Converts content between different formats. 
- Basic formats: txt, html, markdown
- Advanced formats (REQUIRE complete file paths): pdf, docx, rst, latex, epub

1. File Paths - EXPLICIT REQUIREMENTS:
   * When asked to save or convert to a file, you MUST provide:
     - Complete directory path
     - Filename
     - File extension
   * Example request: 'Write a story and save as PDF'
   * You MUST specify: '/path/to/story.pdf' or 'C:\Documents\story.pdf'
   * The tool will NOT automatically generate filenames or extensions

2. File Location After Conversion:
   * After successful conversion, the tool will display the exact path where the file is saved
   * Look for message: 'Content successfully converted and saved to: [file_path]'
   * You can find your converted file at the specified location
   * For better control, always provide explicit output file paths

Note: After conversion, always check the success message for the exact file location.