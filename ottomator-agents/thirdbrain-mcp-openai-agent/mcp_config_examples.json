{"mcpServers": {"timezone": {"command": "python", "args": ["-m", "mcp_server_time", "--local-timezone=America/New_York"], "enable": true}, "weather": {"command": "python", "args": ["./weather-server-python/src/weather/server.py"], "enable": true}, "sequentialthinking": {"command": "docker", "args": ["run", "--rm", "-i", "mcp/sequentialthinking"], "enable": true}, "memory": {"command": "docker", "args": ["run", "-i", "--rm", "mcp/memory"], "env": null, "enable": true}, "fetch": {"command": "docker", "args": ["run", "-i", "--rm", "mcp/fetch"], "env": null, "enable": true}, "mcp-pandoc": {"command": "uvx", "args": ["mcp-pandoc"], "env": null, "enable": true}}}