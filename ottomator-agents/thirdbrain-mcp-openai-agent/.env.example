# For the Supabase version (sample_supabase_agent.py), set your Supabase URL and Service Key.
# Get your SUPABASE_URL from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=

# Set this bearer token to whatever you want. This will be changed once the agent is hosted for you on the Studio!
API_BEARER_TOKEN=

#### OPENAI ################################################
# Get your Open AI API Key by following these instructions -
# https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# You only need this environment variable set if you are using GPT (and not Ollama)
OPENAI_MODEL=gpt-4o-mini
#OPENAI_API_KEY=
OPENAI_URL=https://api.openai.com/v1/

### DEEPSEEK ############################################
# Get your DeepSeek API Key by following these instructions -
# You only need this environment variable set if you are using DeepSeek (and not Ollama or OpenAI)
# do not USE #LLM_MODEL=deepseek-reasoner AS THIS IS NOT SUPPORTED
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_API_KEY=
DEEPSEEK_URL=https://api.deepseek.com/

### OLLAMA ############################################
OLLAMA_MODEL=nezahatkorkmaz/deepseek-v3
OLLAMA_API_KEY=ollama
OLLAMA_URL=https://localhost:11434/v1

###########################################################
# Set this to the openai compatible  model with tool support you want to use.
# Options are:
# - openai
# - deepseek
# - ollama - TODO not tested
SELECTED=DEEPSEEK
