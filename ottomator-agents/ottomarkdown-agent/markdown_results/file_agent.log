2025-01-29 13:56:49,935 - __main__ - INFO - Processing file: test.docx
2025-01-29 13:56:49,937 - __main__ - INFO - Saved content to temporary file: /tmp/temp_file_test.docx
2025-01-29 13:56:49,981 - __main__ - INFO - Successfully converted file. Output length: 4555
2025-01-29 13:56:49,982 - __main__ - INFO - Cleaned up temporary file
2025-01-29 13:56:49,992 - __main__ - INFO - Processing file: test.pptx
2025-01-29 13:56:49,993 - __main__ - INFO - Saved content to temporary file: /tmp/temp_file_test.pptx
2025-01-29 13:56:50,041 - __main__ - INFO - Successfully converted file. Output length: 1565
2025-01-29 13:56:50,041 - __main__ - INFO - Cleaned up temporary file
2025-01-29 13:56:50,052 - __main__ - INFO - Processing file: test.jpg
2025-01-29 13:56:50,052 - __main__ - INFO - Saved content to temporary file: /tmp/temp_file_test.jpg
2025-01-29 13:56:50,052 - __main__ - INFO - Detected image type: webp, using vision model: meta-llama/llama-3.2-11b-vision-instruct:free
2025-01-29 13:56:50,395 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-01-29 13:56:52,161 - __main__ - INFO - Successfully used vision model
2025-01-29 13:56:52,161 - __main__ - INFO - Successfully converted file. Output length: 776
2025-01-29 13:56:52,161 - __main__ - INFO - Cleaned up temporary file
2025-01-29 13:56:52,178 - __main__ - INFO - Processing file: test.pdf
2025-01-29 13:56:52,178 - __main__ - INFO - Saved content to temporary file: /tmp/temp_file_test.pdf
2025-01-29 13:56:52,253 - __main__ - INFO - Successfully converted file. Output length: 28
2025-01-29 13:56:52,253 - __main__ - INFO - Cleaned up temporary file
2025-01-29 13:56:52,267 - __main__ - INFO - Processing file: test.html
2025-01-29 13:56:52,267 - __main__ - INFO - Saved content to temporary file: /tmp/temp_file_test.html
2025-01-29 13:56:52,295 - __main__ - INFO - Successfully converted file. Output length: 10720
2025-01-29 13:56:52,296 - __main__ - INFO - Cleaned up temporary file
2025-01-29 13:56:52,307 - __main__ - INFO - Processing file: test.xlsx
2025-01-29 13:56:52,307 - __main__ - INFO - Saved content to temporary file: /tmp/temp_file_test.xlsx
2025-01-29 13:56:52,377 - __main__ - INFO - Successfully converted file. Output length: 808
2025-01-29 13:56:52,377 - __main__ - INFO - Cleaned up temporary file
2025-02-01 13:49:17,818 - __main__ - INFO - Received request: query='Hi' user_id='google-oauth2|116467443974012389959' request_id='206db4ef-d9ab-4535-a7c9-47818e95a06d' session_id='f38841e8-b0b6-4912-a932-9719157eaf71' files=None
2025-02-01 13:49:18,362 - httpx - INFO - HTTP Request: GET https://gewcfncoqvxnkrbpfonk.supabase.co/rest/v1/messages?select=%2A&session_id=eq.f38841e8-b0b6-4912-a932-9719157eaf71&order=created_at.desc&limit=10 "HTTP/2 200 OK"
2025-02-01 13:49:18,579 - httpx - INFO - HTTP Request: POST https://gewcfncoqvxnkrbpfonk.supabase.co/rest/v1/messages "HTTP/2 400 Bad Request"
2025-02-01 13:49:18,579 - __main__ - WARNING - Schema cache error detected, skipping message storage
2025-02-01 13:49:18,675 - httpx - INFO - HTTP Request: POST https://gewcfncoqvxnkrbpfonk.supabase.co/rest/v1/messages "HTTP/2 400 Bad Request"
2025-02-01 13:49:18,675 - __main__ - WARNING - Schema cache error detected, skipping message storage
2025-02-01 13:51:04,302 - __main__ - INFO - Received request: query='Hi' user_id='google-oauth2|116467443974012389959' request_id='3a755126-575c-4986-b0a1-2bf02d7a2e74' session_id='27f5fcd4-cd2c-483c-b0b6-1c8e2aee2ede' files=None
2025-02-01 13:51:04,675 - httpx - INFO - HTTP Request: GET https://gewcfncoqvxnkrbpfonk.supabase.co/rest/v1/messages?select=%2A&session_id=eq.27f5fcd4-cd2c-483c-b0b6-1c8e2aee2ede&order=created_at.desc&limit=10 "HTTP/2 200 OK"
2025-02-01 13:51:04,812 - httpx - INFO - HTTP Request: POST https://gewcfncoqvxnkrbpfonk.supabase.co/rest/v1/messages "HTTP/2 201 Created"
2025-02-01 13:51:04,945 - httpx - INFO - HTTP Request: POST https://gewcfncoqvxnkrbpfonk.supabase.co/rest/v1/messages "HTTP/2 201 Created"
2025-02-01 13:52:12,054 - __main__ - INFO - Received request: query='Convert this to Markdown' user_id='google-oauth2|116467443974012389959' request_id='d6812a13-b7f4-43d6-bc3a-6e0f1622f864' session_id='9ae00bdd-7ccd-4597-aacb-bd072218319d' files=[{'name': 'test.xlsx', 'type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'base64': '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'}]
2025-02-01 13:52:12,259 - httpx - INFO - HTTP Request: GET https://gewcfncoqvxnkrbpfonk.supabase.co/rest/v1/messages?select=%2A&session_id=eq.9ae00bdd-7ccd-4597-aacb-bd072218319d&order=created_at.desc&limit=10 "HTTP/2 200 OK"
2025-02-01 13:52:12,402 - httpx - INFO - HTTP Request: POST https://gewcfncoqvxnkrbpfonk.supabase.co/rest/v1/messages "HTTP/2 201 Created"
2025-02-01 13:52:12,409 - __main__ - ERROR - Error processing file test.xlsx: [Errno 2] No such file or directory: '/tmp/temp_file_test.xlsx'
2025-02-01 13:52:12,409 - __main__ - INFO - Successfully processed 1 files with query: Convert this to Markdown
2025-02-01 13:52:12,529 - httpx - INFO - HTTP Request: POST https://gewcfncoqvxnkrbpfonk.supabase.co/rest/v1/messages "HTTP/2 201 Created"
