# YouTube Summary Agent - Future Features

## OAuth Features
- [ ] Like videos automatically
- [ ] Mark videos as watched
- [ ] Add/remove videos to/from playlists (pull a video off a playlist and add it to a private "Processed" playlist)

## Enhanced Metadata
- [ ] Video tags/categories
- [ ] Channel subscriber count
- [ ] Related videos
- [ ] Video quality/resolution info
- [ ] Video duration

## Summary Improvements
- [ ] Multiple summary lengths (short/medium/long)
- [ ] Topic extraction
- [ ] Key timestamps with descriptions
- [ ] Sentiment analysis of comments
- [ ] Language detection and translation options
- [ ] Evaluate <PERSON><PERSON>'s Fabric project's "Extract Wisdom" prompt for additional useful improvements

## User Experience
- [x] Support for individual video URLs and playlist URLs with flexible input formats
- [ ] Batch processing of multiple videos
- [ ] Custom summary prompts
- [ ] Scheduled tasks to Send summaries via email or other methods

## Technical Improvements
- [ ] Rate limiting and caching
- [ ] Error retry mechanism
- [ ] Better error messages
- [ ] Performance optimization
- [ ] Automated tests
- [ ] CI/CD pipeline

## Documentation
- [ ] API documentation
- [ ] Setup guide
- [ ] Contributing guidelines
- [ ] Example use cases 