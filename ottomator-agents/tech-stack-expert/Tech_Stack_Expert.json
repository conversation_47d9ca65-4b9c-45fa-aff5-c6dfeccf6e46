{"name": "Tech Stack Expert", "nodes": [{"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "3fffdf1a-0018-4653-ba80-289f39b2cc8f", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1600, 400]}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "81250cea-7055-4b30-90b3-bcbd30dcd7a1", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [760, 400]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "id": "d16dfe63-e92c-4140-904e-70d8a71c71e9", "name": "Prep Output Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1380, 400]}, {"parameters": {"model": "claude-3-5-haiku-********", "options": {}}, "id": "2019ab84-96ae-4f0f-81b2-25d8664db756", "name": "Anthropic <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.2, "position": [900, 620], "credentials": {"anthropicApi": {"id": "AiDvkdxUFBeRQmnE", "name": "Anthropic account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{$('Prep Input Fields').item.json.session_id}}", "tableName": "messages", "contextWindowLength": 10}, "id": "2777b8be-392b-47f3-bad0-774e53b2cd7d", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [1040, 620], "credentials": {"postgres": {"id": "erIa9T64hNNeDuvB", "name": "Prod Postgres account"}}}, {"parameters": {"httpMethod": "POST", "path": "tech-stack-expert", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "0ef6b0a3-d069-435c-a781-4f3e467b4c56", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [500, 400], "webhookId": "b64eb7c7-350f-4477-a56b-ffcd9d19dc2f", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"promptType": "define", "text": "={{$('Prep Input Fields').item.json.query}}", "options": {"systemMessage": "=You are a tech stack expert and help users figure out what their tech stack should be for their full stack applications. Your goal is to guide users through a conversation to get the information you need from them to best define the ideal tech stack for their use case.\n\nIMPORTANT: Only ask one question at a time.\n\n1. First ask them to describe the application they are looking to build.\n\n2. Next ask them if they are using AI coding assistants, ask if they are using\n- Bolt.new\n- Bolt.diy\n- Windsurf\n- Lovable\n- Cursor\n- Aider\n- Another AI Coding Assistant\n\nIf they are using Bolt.new, Bolt.diy, or Lovable, assume their frontend needs to be a React frontend with Vite. Otherwise, you aren't sure at this point. You also don't know about the backend yet.\n\n3. Next ask them which technologies they have experience with for the frontend.\n\n4. Next ask them the same thing but for the backend.\n\n5. Next ask them how many users will be using the platform.\n- 1-100\n- 100-1,000\n- 1,000-10,000\n- 10,000+\n\n6. Next ask if there are any technologies they definitely need to use for the frontend, backend, authentication, the LLM, or cloud hosting. Or anything else that is a requirement.\n\nOnce you have all this information, generate them a full tech stack including:\n\n1. Frontend\n\nFor very simple applications with not a lot of users, recommend Streamlit unless they are building with a browser AI coding assistant like <PERSON>lt.new, Bolt.diy, or Lovable.\n\nFor more complicated apps or ones with more users, recommend Next.js or React/Svelte/Vue with Vite.\n\n2. Backend\n\nRecommend n8n/Flowise for building AI agents for simple use cases, otherwise recommend LangChain + LangGraph with either Python or JavaScript.\n\nThe backend should be Python or JavaScript mostly, only recommend Go if they need something very fast that doesn't have AI integrated.\n\nFor APIs, recommend FastAPI for Python and Express for JavaScript.\n\n3. Authentication\n\nRecommend Supabase authentication for most use cases unless it seems more complex/multiple parts of the site that could benefit from the robustness and SSO offered by Auth0.\n\n4. Database\n\nRecommend a SQL database for most use cases unless it really makes sense to use Firebase/MongoDB. Supabase is the go to for SQL and also using PGVector for RAG.\n\n5. LLM\n\nRecommend a Claude model (3.5 Sonnet or 3.5 Haiku depending on cost/speed) if there isn't private data. Otherwise, recommend using Ollama for a local model like Qwen 2.5 or Qwen 2.5 Coder 32b. Recommend Llama 3.2 for a vision model.\n\n\nFor all parts above, prioritize recommending what they are comfortable with/tied to unless it really doesn't make sense."}}, "id": "004e882e-5b15-4cb7-849f-12b46d8bf4ca", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1000, 400]}, {"parameters": {"content": "# Tech Stack Expert Agent\n\nAuthor: [<PERSON>](https://www.youtube.com/@ColeMedin)\n\nA specialized n8n agent that helps users determine the ideal tech stack for their full-stack applications. This agent demonstrates how to create a basic conversational AI assistant in n8n.\n\n## Features\n\n- Conducts guided conversations to understand project requirements\n- Considers user experience with different technologies\n- Provides tailored recommendations for:\n  - Frontend frameworks\n  - Backend technologies\n  - Authentication solutions\n  - Database systems\n  - LLM integration\n- Maintains conversation context using Postgres chat memory", "height": 492.97877906976817, "width": 651.0139534883727, "color": 6}, "id": "cc7c1740-58e0-49d8-8280-e20af4f83a9e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-240, 320]}], "pinData": {}, "connections": {"Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Prep Input Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "b91df2fa-84f5-428e-80ad-7ceb908040af", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "5xKTEbm6pqK7Y8fd", "tags": [{"createdAt": "2024-12-09T14:35:20.507Z", "updatedAt": "2024-12-09T14:35:20.507Z", "id": "wBUwbX8AS8QmBHOC", "name": "studio-prod"}]}