{"name": "Agent <PERSON>", "nodes": [{"parameters": {"options": {}}, "id": "ece04891-3085-4ae0-8624-249163d16313", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [4400, 2000], "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [4800, 2000], "id": "9e24b4b1-01d8-4014-908b-d5c850acb93b", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"authentication": "oAuth2", "operation": "sendAndWait", "user": {"__rl": true, "value": "U08S4Q86MR7", "mode": "list", "cachedResultName": "cole"}, "message": "=please APPROVE or DECLINE this message:\n\n{{ $('When Executed by Another Workflow').item.json.message }}", "approvalOptions": {"values": {"approvalType": "double"}}, "options": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3100, 1920], "id": "0e195e86-9740-4e45-a481-e3c60a3b6bfb", "name": "<PERSON><PERSON>ck", "webhookId": "fec140b6-b33d-4d9f-b48a-f7e84ebe0bfb", "credentials": {"slackOAuth2Api": {"id": "aIFdokXUeAtAOjW0", "name": "Slack account"}}}, {"parameters": {"model": "gpt-4.1-mini", "options": {}}, "id": "42d1ec0f-380e-4a35-9390-36e250efaf1a", "name": "OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [2340, 2060], "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "id": "0a675740-e73f-4332-b2dc-e628e2499676"}], "combinator": "and"}, "renameOutput": true, "outputKey": "APPROVED"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8b61a1ad-7442-429b-a541-3f5d280a0788", "leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "DENIED"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [3340, 1920], "id": "60bd7d77-2049-445b-81e3-00b356101390", "name": "Switch1"}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08RSGUJK1R", "mode": "list", "cachedResultName": "research"}, "text": "=Here is the APPROVED message:  \n\n{{ $('When Executed by Another Workflow').item.json.message }}", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3620, 1800], "id": "65fd189b-150f-4cdb-bcc3-2508222a69f7", "name": "Slack1", "webhookId": "ceaa4e78-596e-418f-9218-718534897b0d", "credentials": {"slackOAuth2Api": {"id": "aIFdokXUeAtAOjW0", "name": "Slack account"}}}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08RSGUJK1R", "mode": "list", "cachedResultName": "research"}, "text": "=Here is the DECLINED message: \n\n{{ $('When Executed by Another Workflow').item.json.message }}", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3620, 2020], "id": "036dfd39-9bfd-4d7f-84d6-41121e1284cb", "name": "Slack2", "webhookId": "ff85accd-3fba-4eb5-a558-e8e8309096ee", "credentials": {"slackOAuth2Api": {"id": "aIFdokXUeAtAOjW0", "name": "Slack account"}}}, {"parameters": {"model": "gpt-4.1-mini", "options": {}}, "id": "792ccb86-263c-4145-8712-24fd93af3ae9", "name": "OpenAI Chat Model4", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-960, 2040], "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"errorMessage": "An error has occured!"}, "type": "n8n-nodes-base.stopAndError", "typeVersion": 1, "position": [7200, 2020], "id": "df755e83-8410-4bbf-ad94-574e94ee2992", "name": "Stop and Error", "executeOnce": false, "alwaysOutputData": false, "notesInFlow": false, "onError": "continueRegularOutput"}, {"parameters": {"authentication": "oAuth2", "operation": "sendAndWait", "user": {"__rl": true, "value": "U08S4Q86MR7", "mode": "list", "cachedResultName": "cole"}, "message": "=Please APPROVE or DECLINE this message", "approvalOptions": {"values": {"approvalType": "double"}}, "options": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [6740, 1900], "id": "ff3bd817-8e25-4013-882c-de268fd4b5f6", "name": "Slack3", "webhookId": "fec140b6-b33d-4d9f-b48a-f7e84ebe0bfb", "credentials": {"slackOAuth2Api": {"id": "aIFdokXUeAtAOjW0", "name": "Slack account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "id": "0a675740-e73f-4332-b2dc-e628e2499676"}], "combinator": "and"}, "renameOutput": true, "outputKey": "APPROVED"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8b61a1ad-7442-429b-a541-3f5d280a0788", "leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "DENIED"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [6940, 1900], "id": "1206e685-c523-4d9b-8112-89c9807623ff", "name": "Switch2"}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08RSGUJK1R", "mode": "list", "cachedResultName": "research"}, "text": "=Message is approved", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [7200, 1800], "id": "214787f9-b740-4e20-8604-2818f1422cec", "name": "Slack4", "webhookId": "ff85accd-3fba-4eb5-a558-e8e8309096ee", "credentials": {"slackOAuth2Api": {"id": "aIFdokXUeAtAOjW0", "name": "Slack account"}}}, {"parameters": {}, "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [7420, 1920], "id": "9a09e569-76c2-42f9-8eb5-6bb595a1953a", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [6560, 1900], "id": "9dfb40f6-67c2-4abb-aaa5-187bc27b97d3", "name": "When clicking ‘Test workflow’"}, {"parameters": {"promptType": "define", "text": "={{ $json.output }}", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=\nYou are a standalone food critic Agent. You operate as the final check in the workflow to ensure that the final response follows the following guidelines for Output Check, Format Validator, Content Filter:\n\n\t•\tContent Filter – Ensure the response includes where the dish originates and a description of the dish \n\nEXAMPLE: \nName: Moussaka\nDescription: Originating in Greece, moussaka is a layered casserole of tender eggplant slices and spiced ground lamb (or beef), all topped with a silky béchamel sauce and baked until golden and bubbling. It’s a comforting dish that combines Mediterranean flavors—olive oil, cinnamon, and tomato—with a rich, creamy finish.\n\n\n\t•\tOutput Check – ensure name and description are present \n\t\nOutput false for 'valid' and your feedback for 'feedback' if the response from the other agent didn't follow the guidelines for the output check, content filter, etc.\n\nOutput true for 'valid' if it did. Don't be too picky.\n\n"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [4820, 1800], "id": "813ac4e1-2637-4eab-9dab-d7a4b27d3be1", "name": "Critic Node"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appu9xcTfFpUJ1pwp", "mode": "list", "cachedResultName": "Restaurant", "cachedResultUrl": "https://airtable.com/appu9xcTfFpUJ1pwp"}, "table": {"__rl": true, "value": "tblb2LYwGNnjfF9eb", "mode": "list", "cachedResultName": "Dishes", "cachedResultUrl": "https://airtable.com/appu9xcTfFpUJ1pwp/tblb2LYwGNnjfF9eb"}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [2160, 560], "id": "c1be2df1-90d3-4d32-b733-1a2c1a0324e1", "name": "Menu Table", "credentials": {"airtableTokenApi": {"id": "GsTR1ZYQmElJWLUF", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"content": "## <PERSON><PERSON>, <PERSON>, and Tool Nodes\n", "height": 485, "width": 656, "color": 4}, "id": "5e4c4532-410e-4fe6-9b63-75602247cff3", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1060, 1720]}, {"parameters": {"content": "## User Input and Control Nodes\n\n", "height": 545, "width": 1656, "color": 6}, "id": "3c1183ae-3396-4d5c-b41c-d312415698a9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2220, 1700]}, {"parameters": {"content": "## Guardrail Node", "height": 565, "width": 1556}, "id": "ba1a9e46-dca6-4c2b-98d9-aaf8ba2fe2d3", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4320, 1700]}, {"parameters": {"content": "## Fallback Node\n", "height": 525, "width": 1416, "color": 3}, "id": "6e46a3d0-bad8-43c5-bea2-3beadb2f7791", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [6460, 1720]}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [-780, 2040], "id": "722d3e32-58be-4667-b5d6-d105e3e2dd73", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "ezhtjmm7WxmXJLU8", "name": "Self Hosted Supabase"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "=here is the current date/time : {{ $now }}"}}, "id": "c9f389de-8e6c-4513-8490-6e96a452a66c", "name": "AI Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-860, 1820]}, {"parameters": {"model": "gpt-4.1-mini", "options": {}}, "id": "0e25d4f7-c69f-486a-bf84-09a094fe7225", "name": "OpenAI Chat Model8", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [260, 2040], "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"content": "## More Detailed Memory Nodes\n", "height": 505, "width": 1676, "color": 5}, "id": "38c6c3e9-8790-4b2d-a22f-a3904f63c2f2", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [80, 1720]}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('When chat message received').item.json.sessionId }}"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [440, 2040], "id": "3b01a694-3e29-4e66-bad1-1375c9af6c5f", "name": "Postgres Chat Memory1", "credentials": {"postgres": {"id": "ezhtjmm7WxmXJLU8", "name": "Self Hosted Supabase"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [700, 2040], "id": "e9a7578a-370f-4f93-bf77-138deac6853d", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"operation": "get", "documentURL": "15sBOoqCJjIggmtG9aEFPFe3AFS_G6iMOZQmT6wkkRVM"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [160, 1820], "id": "9fe93f1f-f7af-4f56-a60b-f03dd169e53c", "name": "Get Memories", "credentials": {"googleDocsOAuth2Api": {"id": "tJPWOFJCX9kAe4Zv", "name": "Google Docs account"}}}, {"parameters": {"operation": "update", "documentURL": "15sBOoqCJjIggmtG9aEFPFe3AFS_G6iMOZQmT6wkkRVM", "actionsUi": {"actionFields": [{"action": "insert", "text": "={{ $json.text }}"}]}}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1360, 1820], "id": "08c53829-9c6c-4a19-9d50-************", "name": "Save Memories", "credentials": {"googleDocsOAuth2Api": {"id": "tJPWOFJCX9kAe4Zv", "name": "Google Docs account"}}}, {"parameters": {"promptType": "define", "text": "=User message:\n\n{{ $('When chat message received').item.json.chatInput }}\n\nBased on the user message above, extract any key memories that should be stored for later converastions. Just output nothing (an empty string) if there are no important memories to be made here. Otherwise if there are good memories, output just those to store directly in the memory bank"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [740, 1820], "id": "307da2bf-3f0c-419d-ae06-894be3658497", "name": "Extract Memories"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ffe35d80-0ebf-4b26-b292-dc870fd83413", "leftValue": "={{ $json.text }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1100, 1820], "id": "6f9fa9b7-4001-4083-8832-3bf24430d244", "name": "If"}, {"parameters": {"assignments": {"assignments": [{"id": "1ea78aec-3a4d-48f2-bf20-ba69229646f0", "name": "output", "value": "={{ $('AI Agent2').item.json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1540, 2020], "id": "103fc4ab-2e8a-4870-a599-e09ecbf20d03", "name": "<PERSON>"}, {"parameters": {"promptType": "define", "text": "={{ $('When chat message received').item.json.chatInput }}", "options": {"systemMessage": "You are an AI agent that recommeds dishes you don't do anything else."}}, "id": "e0cb6792-8940-4373-946f-1fe772ea0558", "name": "AI Agent4", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [4440, 1800]}, {"parameters": {"promptType": "define", "text": "=Here are your long term memories:\n\n{{ $json.content }}\n\nThen here is the user message, this is what you need to respond to:\n\n{{ $('When chat message received').item.json.chatInput }}", "options": {"systemMessage": "=here is the current date/time : {{ $now }}"}}, "id": "0e4f7b89-c1bf-4d9d-8261-a433db6d9107", "name": "AI Agent2", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [380, 1820]}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": ""}}, "id": "31b2544f-a10e-42d9-846f-b5018a9fd7fb", "name": "AI Agent3", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [2380, 1840]}, {"parameters": {"jsonSchemaExample": "{\n\t\"valid\": false,\n\t\"feedback\": \"Make sure you include the origin of the dish!\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [5000, 2000], "id": "2372ff88-525e-4441-bae9-3f837d31e55f", "name": "Structured Output Parser3"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7dbaedb8-9254-4df9-be0c-2895594ce919", "leftValue": "={{ $json.output.valid }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [5180, 1800], "id": "90e38fbc-ddde-409a-8b9f-85845dd6145b", "name": "If1"}, {"parameters": {"assignments": {"assignments": [{"id": "8649ff8f-56ad-431a-a701-2f3a06ca43d9", "name": "output", "value": "={{ $('AI Agent4').item.json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [5460, 1760], "id": "d2396497-528d-44c1-96a7-e0e288ef4d4e", "name": "Edit Fields1"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('When chat message received').item.json.sessionId }}"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [4560, 2000], "id": "********-60ea-4b59-bd26-337d66405f41", "name": "Postgres Chat Memory2", "credentials": {"postgres": {"id": "ezhtjmm7WxmXJLU8", "name": "Self Hosted Supabase"}}}, {"parameters": {"options": {}}, "id": "26923d2a-da6f-452e-91fe-9bba2f1f8c71", "name": "OpenAI Chat Model9", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [5320, 2120], "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "=Any feedback from the critque agent:\n\n{{ $json?.output?.feedback || \"\" }}\n\nUser message:\n\n{{ $('When chat message received').item.json.chatInput }}", "options": {"systemMessage": "You are an AI agent that recommeds dishes you don't do anything else."}}, "id": "e093cb43-00cd-4258-b023-0374b7ca79a0", "name": "AI Agent5", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [5380, 1940]}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('When chat message received').item.json.sessionId }}"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [5480, 2120], "id": "b0b7f6c7-d42b-4fdb-a7c6-aedcc9f5fac7", "name": "Postgres Chat Memory4", "credentials": {"postgres": {"id": "ezhtjmm7WxmXJLU8", "name": "Self Hosted Supabase"}}}, {"parameters": {"authentication": "oAuth2", "select": "user", "user": {"__rl": true, "value": "U08S4Q86MR7", "mode": "list", "cachedResultName": "cole"}, "text": "={{ $json.execution.error.message }}", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [7620, 1920], "id": "99df8edd-0391-44a6-9051-06fb560941d3", "name": "Slack7", "webhookId": "ff85accd-3fba-4eb5-a558-e8e8309096ee", "credentials": {"slackOAuth2Api": {"id": "aIFdokXUeAtAOjW0", "name": "Slack account"}}}, {"parameters": {"name": "send_slack_message", "description": "Sends a slack message based on the message you provide", "workflowId": {"__rl": true, "mode": "id", "value": "={{$workflow.id}}", "cachedResultName": "={{$workflow.id}}"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('message', ``, 'string') }}"}, "matchingColumns": ["message"], "schema": [{"id": "message", "displayName": "message", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.1, "position": [2580, 2060], "id": "805905a4-e56b-4284-bc95-f492df1af7db", "name": "Call n8n Workflow Tool"}, {"parameters": {"workflowInputs": {"values": [{"name": "message"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [2840, 1920], "id": "1f7fc925-4ba4-4043-9dc1-aa330b5ba7c4", "name": "When Executed by Another Workflow"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appu9xcTfFpUJ1pwp", "mode": "list", "cachedResultName": "Restaurant", "cachedResultUrl": "https://airtable.com/appu9xcTfFpUJ1pwp"}, "table": {"__rl": true, "value": "tblb2LYwGNnjfF9eb", "mode": "list", "cachedResultName": "Dishes", "cachedResultUrl": "https://airtable.com/appu9xcTfFpUJ1pwp/tblb2LYwGNnjfF9eb"}, "columns": {"mappingMode": "defineBelow", "value": {"Dish Name": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Dish_Name', ``, 'string') }}", "Dish Description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Dish_Description', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "Dish Name", "displayName": "Dish Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Dish Description", "displayName": "Dish Description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [-600, 2040], "id": "a516c86b-57a5-4300-8184-afbde021771e", "name": "Airtable2", "credentials": {"airtableTokenApi": {"id": "GsTR1ZYQmElJWLUF", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"options": {}}, "id": "2b5c8aa1-42e6-4123-80f0-a7bf3bf7ae86", "name": "OpenAI Chat Model5", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [1820, 560], "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"content": "## Full Example", "height": 765, "width": 3116}, "id": "0d501fbf-8f09-472f-9182-f1fed473f7be", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1460, 200]}, {"parameters": {"promptType": "define", "text": "=Your memories from the user:\n\n{{ $json.content }}\n\nThe user request:\n\n{{ $('When chat message received').item.json.chatInput }}", "hasOutputParser": true, "options": {"systemMessage": "You are an AI agent that recommeds dishes you don't do anything else."}}, "id": "********-4f52-457c-a5b6-cbcb51ae732c", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [1980, 320]}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('When chat message received').item.json.sessionId }}"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [2020, 560], "id": "9a70b4ec-91d1-4113-8246-e67d1e61c058", "name": "Postgres Chat Memory5", "credentials": {"postgres": {"id": "ezhtjmm7WxmXJLU8", "name": "Self Hosted Supabase"}}}, {"parameters": {"jsonSchemaExample": "{\n\t\"dish name\": \"Harvest Moon Risotto\",\n\t\"dish description\": \"A creamy Arborio rice dish studded with tender roasted butternut squash and wilted spinach, enriched by a sage-infused brown butter and finished with a shower of freshly grated Parmesan and toasted pepitas for a nutty crunch.\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2420, 780], "id": "8c2fc9a2-f226-4565-9440-272ae2986453", "name": "Structured Output Parser4"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "typeVersion": 1, "position": [2320, 560], "id": "b8674dfd-902c-498d-9f16-4f3ae48c4b26", "name": "Auto-fixing Output Parser1"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2240, 760], "id": "6c6909f4-17a0-47fe-8b63-36f073c02760", "name": "OpenAI Chat Model13", "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [3360, 560], "id": "5f30ab05-9c76-4569-a0e2-a4f24251023a", "name": "OpenAI Chat Model10", "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"operation": "get", "documentURL": "15sBOoqCJjIggmtG9aEFPFe3AFS_G6iMOZQmT6wkkRVM"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1760, 320], "id": "939438cf-08ea-49a1-9432-5079e8e25bf3", "name": "Get Memories1", "credentials": {"googleDocsOAuth2Api": {"id": "tJPWOFJCX9kAe4Zv", "name": "Google Docs account"}}}, {"parameters": {"operation": "update", "documentURL": "15sBOoqCJjIggmtG9aEFPFe3AFS_G6iMOZQmT6wkkRVM", "actionsUi": {"actionFields": [{"action": "insert", "text": "={{ $json.text }}"}]}}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [3980, 340], "id": "7d142773-9fab-4e47-9163-75aaf8a78018", "name": "Save Memories1", "credentials": {"googleDocsOAuth2Api": {"id": "tJPWOFJCX9kAe4Zv", "name": "Google Docs account"}}}, {"parameters": {"promptType": "define", "text": "=User message:\n\n{{ $('When chat message received').item.json.chatInput }}\n\nBased on the user message above, extract any key memories that should be stored for later converastions. Just output nothing (an empty string) if there are no important memories to be made here. Otherwise if there are good memories, output just those to store directly in the memory bank"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [3420, 340], "id": "83c6dc72-c6f4-4768-85b0-4f05fd71582e", "name": "Extract Memories1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ffe35d80-0ebf-4b26-b292-dc870fd83413", "leftValue": "={{ $json.text }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [3780, 460], "id": "653b3871-9876-4318-b4cb-af9b224dcfd5", "name": "If2"}, {"parameters": {"errorMessage": "The dish has been rejected!"}, "type": "n8n-nodes-base.stopAndError", "typeVersion": 1, "position": [2980, 600], "id": "e5db54c6-9c66-4895-8daf-1db6cacd1fe1", "name": "Stop and Error2", "executeOnce": false, "alwaysOutputData": false, "notesInFlow": false, "onError": "continueRegularOutput"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "id": "0a675740-e73f-4332-b2dc-e628e2499676"}], "combinator": "and"}, "renameOutput": true, "outputKey": "APPROVED"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8b61a1ad-7442-429b-a541-3f5d280a0788", "leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "DENIED"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [2680, 420], "id": "f314b057-47ae-41dc-980a-298e8bfedf73", "name": "Switch"}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08RSGUJK1R", "mode": "list", "cachedResultName": "research"}, "text": "=[CONFIRMED] here is the APPROVED dish:\n\ndish name: {{ $('AI Agent').item.json.output['dish name'] }}\n\ndish description: {{ $('AI Agent').item.json.output['dish description'] }}", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2980, 340], "id": "4ade5845-90f6-4a0c-802b-a77c81a7123d", "name": "Slack8", "webhookId": "ff85accd-3fba-4eb5-a558-e8e8309096ee", "credentials": {"slackOAuth2Api": {"id": "aIFdokXUeAtAOjW0", "name": "Slack account"}}}, {"parameters": {"authentication": "oAuth2", "operation": "sendAndWait", "user": {"__rl": true, "value": "U08S4Q86MR7", "mode": "list", "cachedResultName": "cole"}, "message": "=Please APPROVE or DECLINE this dish based on ingredient availability\n\ndish name: {{ $json.output['dish name'] }}\n\ndish discription: {{ $json.output['dish description'] }}", "approvalOptions": {"values": {"approvalType": "double"}}, "options": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2420, 320], "id": "6c57a7de-4d51-4960-a64b-c92d5779cfd6", "name": "Slack9", "webhookId": "fec140b6-b33d-4d9f-b48a-f7e84ebe0bfb", "credentials": {"slackOAuth2Api": {"id": "aIFdokXUeAtAOjW0", "name": "Slack account"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appu9xcTfFpUJ1pwp", "mode": "list", "cachedResultName": "Restaurant", "cachedResultUrl": "https://airtable.com/appu9xcTfFpUJ1pwp"}, "table": {"__rl": true, "value": "tblb2LYwGNnjfF9eb", "mode": "list", "cachedResultName": "<PERSON><PERSON>", "cachedResultUrl": "https://airtable.com/appu9xcTfFpUJ1pwp/tblb2LYwGNnjfF9eb"}, "columns": {"mappingMode": "defineBelow", "value": {"Dish Name": "={{ $('AI Agent').item.json.output['dish name'] }}", "Dish Description": "={{ $('AI Agent').item.json.output['dish description'] }}"}, "matchingColumns": [], "schema": [{"id": "Dish Name", "displayName": "Dish Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Dish Description", "displayName": "Dish Description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [3180, 340], "id": "53345af8-3613-4f90-a4c5-c9563aac30b1", "name": "Airtable", "credentials": {"airtableTokenApi": {"id": "GsTR1ZYQmElJWLUF", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"promptType": "define", "text": "=Summarize the dish created:\n\nName: {{ $('AI Agent').item.json.output['dish name'] }}\nDescription: {{ $('AI Agent').item.json.output['dish description'] }}\n\nAnd also give a sentence about how tasty the dish is and what makes it so good."}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [4200, 500], "id": "11f55028-1a6f-4451-829b-96439f0fa96c", "name": "Basic LLM Chain"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [4280, 680], "id": "d5b284e9-640d-44c1-9d76-f86c3da96cee", "name": "OpenAI Chat Model6", "credentials": {"openAiApi": {"id": "KnSSZQEFv82oFpIv", "name": "OpenAi account"}}}, {"parameters": {"content": "![image](https://i.imgur.com/JNSaaiN.png)", "height": 360, "width": 560, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1020, 1320], "id": "26b2c5cf-5b7d-4ac4-9975-cd33b8f8cde5", "name": "Sticky Note1"}, {"parameters": {"content": "![image](https://i.imgur.com/zeStg3B.png)", "height": 380, "width": 1060, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [380, 1320], "id": "99a0121b-712a-4418-bfb0-b06437d174e3", "name": "Sticky Note2"}, {"parameters": {"content": "![image](https://i.imgur.com/519Vkq6.png)", "height": 460, "width": 1000, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2520, 1220], "id": "5359216e-986f-4810-bbc3-52ec564c5628", "name": "Sticky Note3"}, {"parameters": {"content": "![image](https://i.imgur.com/zaIvptH.png)", "height": 380, "width": 860, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [6720, 1320], "id": "90e8dad2-a2b7-4d8b-b1e4-be123fdd7931", "name": "Sticky Note5"}, {"parameters": {"content": "![image](https://i.imgur.com/7eLXIMZ.png)", "height": 340, "width": 1200}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4480, 1340], "id": "61782241-ef1c-4465-9cc5-b707e66ffd08", "name": "Sticky Note4"}, {"parameters": {"content": "![image](https://i.imgur.com/GQDk7ED.png)", "height": 520, "width": 1240}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2420, -340], "id": "c562ccf3-9b6d-4fe3-b8e8-9bade0aa4d75", "name": "Sticky Note7"}, {"parameters": {"content": "# 🤖 The Seven Node Blueprint for AI Agents 🤖\n\n## **Agents are just graphs.**\n\nViewing AI agents as graphs provides a powerful mental model for designing systems. This approach breaks down complex agents into interconnected nodes, each with specific responsibilities. By thinking in graphs, we can:\n\n- **Modularize functionality** for better maintenance\n- **Scale complexity** through simple component connections\n- **Improve resilience** with explicit error handling paths\n- **Enable transparency** by making information flow visible\n\nThis is how you can design complex systems with ease (when necessary).\n\n## Node Types\n\n### 1️⃣ LLM Node 🧠\n\n**Purpose**: Reasoning, generation, and decision-making\n\n**Examples**: Planning, content generation, summarization\n\n**Why it's useful**: Acts as the \"brain\" that processes information and coordinates between components, handling ambiguity and novel situations.\n\n### 2️⃣ Tool Node 🛠️\n\n**Purpose**: Executes external tools or APIs\n\n**Examples**: Web search, code execution, database queries\n\n**Why it's useful**: Extends capabilities beyond language processing, connecting the agent to external systems and real-time data.\n\n### 3️⃣ Control Node ⚙️\n\n**Purpose**: Handles logic, branching, and deterministic rules\n\n**Examples**: Routing, conditionals, filters\n\n**Why it's useful**: Introduces predictability and enables consistent handling of different cases and business logic.\n\n### 4️⃣ Memory Node 📚\n\n**Purpose**: Reads/writes to memory for stateful context\n\n**Examples**: Vector DBs, conversation history, user preferences\n\n**Why it's useful**: Maintains context over time, enabling personalization and self-evolution.\n\n### 5️⃣ Guardrail Node 🚧\n\n**Purpose**: Validates outputs and enforces constraints\n\n**Examples**: Content filtering, format validation, safety checks\n\n**Why it's useful**: Ensures outputs meet required standards, preventing harmful or incorrect responses.\n\n### 6️⃣ Fallback Node 🔄\n\n**Purpose**: Responds to failures with retries or alternate flows\n\n**Examples**: Retries, escalation paths, default responses\n\n**Why it's useful**: Provides graceful degradation when things go wrong rather than complete failure.\n\n### 7️⃣ User Input Node 👥\n\n**Purpose**: Brings humans into the loop for clarification or decisions\n\n**Examples**: Confirmations, clarification requests, approvals\n\n**Why it's useful**: Leverages human judgment for critical situations where AI might not be sufficient.\n\n## Building with the Blueprint\n\nWhen designing agents as graphs:\n\n1. **Start simple**, then add complexity as needed\n2. **Identify natural breakpoints** for different node types\n3. **Plan for failures** with fallbacks and validation\n4. **Balance automation with control** through human touchpoints\n5. **Visualize the flow** to spot bottlenecks or unnecessary complexity\n\nBy thoughtfully combining these node types, we can build AI systems that are more capable, reliable, and aligned with human needs.", "height": 1940, "width": 960, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2460, 100], "id": "97211dd6-aaf1-44b2-a8bd-dee5501b6b41", "name": "Sticky Note14"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [1320, 320], "id": "d45bdd05-775e-42e6-b984-afc4aeab543a", "name": "When chat message received", "webhookId": "7b60c4b1-d0df-4d52-a046-5597a516bd32"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent4", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Critic Node", "type": "ai_languageModel", "index": 0}]]}, "Slack": {"main": [[{"node": "Switch1", "type": "main", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "AI Agent3", "type": "ai_languageModel", "index": 0}]]}, "Switch1": {"main": [[{"node": "Slack1", "type": "main", "index": 0}], [{"node": "Slack2", "type": "main", "index": 0}]]}, "OpenAI Chat Model4": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Slack3": {"main": [[{"node": "Switch2", "type": "main", "index": 0}]]}, "Switch2": {"main": [[{"node": "Slack4", "type": "main", "index": 0}], [{"node": "Stop and Error", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "Slack7", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Slack3", "type": "main", "index": 0}]]}, "Menu Table": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model8": {"ai_languageModel": [[{"node": "AI Agent2", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory1": {"ai_memory": [[{"node": "AI Agent2", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Extract Memories", "type": "ai_languageModel", "index": 0}]]}, "Get Memories": {"main": [[{"node": "AI Agent2", "type": "main", "index": 0}]]}, "Extract Memories": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Save Memories", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Save Memories": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "AI Agent4": {"main": [[{"node": "Critic Node", "type": "main", "index": 0}]]}, "AI Agent2": {"main": [[{"node": "Extract Memories", "type": "main", "index": 0}]]}, "AI Agent3": {"main": [[]]}, "Structured Output Parser3": {"ai_outputParser": [[{"node": "Critic Node", "type": "ai_outputParser", "index": 0}]]}, "Critic Node": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}], [{"node": "AI Agent5", "type": "main", "index": 0}]]}, "Postgres Chat Memory2": {"ai_memory": [[{"node": "AI Agent4", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model9": {"ai_languageModel": [[{"node": "AI Agent5", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory4": {"ai_memory": [[{"node": "AI Agent5", "type": "ai_memory", "index": 0}]]}, "Call n8n Workflow Tool": {"ai_tool": [[{"node": "AI Agent3", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Airtable2": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model5": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Slack9", "type": "main", "index": 0}]]}, "Postgres Chat Memory5": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Structured Output Parser4": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser1", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat Model13": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser1", "type": "ai_languageModel", "index": 0}]]}, "Auto-fixing Output Parser1": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat Model10": {"ai_languageModel": [[{"node": "Extract Memories1", "type": "ai_languageModel", "index": 0}]]}, "Save Memories1": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Get Memories1": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Extract Memories1": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "Save Memories1", "type": "main", "index": 0}], [{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Slack8", "type": "main", "index": 0}], [{"node": "Stop and Error2", "type": "main", "index": 0}]]}, "Slack8": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Slack9": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "Extract Memories1", "type": "main", "index": 0}]]}, "OpenAI Chat Model6": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Get Memories1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "7302c076-96d7-4e9f-bfc9-bc7d4b418a86", "meta": {"templateCredsSetupCompleted": true, "instanceId": "283ffa8d05f0aea5fd537be52c7be659b3a880d9175d738aa61bb7798a0dea8e"}, "id": "kvvJioOStPu63Lh8", "tags": []}