# Options supported are:
# 1. OpenAI
# 2. OpenRouter
# 3. Ollama
PROVIDER=

# Base URL for the OpenAI instance (default is https://api.openai.com/v1)
# OpenAI: https://api.openai.com/v1
# Ollama (example): http://localhost:11434/v1
# OpenRouter: https://openrouter.ai/api/v1
BASE_URL=

# OpenAI: https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# Open Router: Get your API Key here after registering: https://openrouter.ai/keys
# Ollama: No need to set this unless you specifically configured an API key
LLM_API_KEY=

# The LLM you want to use for the agents. Make sure this LLM supports tools (especially important if using Ollama)!
# OpenAI example: gpt-4.1-mini
# OpenRouter example: anthropic/claude-3.7-sonnet
# Ollama example: mistrall-small3.1
MODEL_CHOICE=

# Langfuse secret key - get this in the "setup" page after creating a project in Langfuse (only visible once)
LANGFUSE_SECRET_KEY=

# Langfuse public key - get this in the "setup" page as well, also available later on
LANGFUSE_PUBLIC_KEY=

# Langfuse host - for the local AI package this will be http://localhost:3002
LANGFUSE_HOST=

# MCP Server API Keys
# ==================

# Brave Search API Key
# Get your Brave API key by going to the following link after signing up for Brave:
# https://api.search.brave.com/app/keys
BRAVE_API_KEY=

# Airtable API Key
# Get your API key from https://airtable.com/account
AIRTABLE_API_KEY=

# GitHub API Token
# Generate a personal access token at https://github.com/settings/tokens
GITHUB_TOKEN=

# Slack API Tokens
# Create a Slack app at https://api.slack.com/apps and obtain the tokens
# Even better, follow the 'Setup' instructions given in the Slack MCP server GitHub page:
# https://github.com/modelcontextprotocol/servers/tree/main/src/slack#setup
SLACK_BOT_TOKEN=
SLACK_TEAM_ID=

# Firecrawl API Key
# Register for an API key at https://firecrawl.dev
FIRECRAWL_API_KEY=

# MCP Server Config
# ==================

# The folder you want exposed to the file system agent
LOCAL_FILE_DIR=