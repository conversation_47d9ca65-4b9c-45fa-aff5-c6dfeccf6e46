# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Brave Search API Configuration
BRAVE_API_KEY=your_brave_api_key_here

# Twitter API Configuration (OAuth 1.0a)
# Get these from https://developer.twitter.com/en/portal/dashboard
TWITTER_API_KEY=your_api_key_here
TWITTER_API_SECRET=your_api_secret_here
TWITTER_ACCESS_TOKEN=your_access_token_here
TWITTER_ACCESS_TOKEN_SECRET=your_access_token_secret_here

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_key_here

API_BEARER_TOKEN=

# Twitter API Setup Instructions:
# 1. Go to https://developer.twitter.com/en/portal/dashboard
# 2. Select your project and app (or create new ones)
# 3. Go to 'Settings' > 'User authentication settings'
# 4. Set app permissions to "Read and Write"
# 5. Go to 'Keys and tokens':
#    - Under 'Consumer Keys', copy API Key and Secret
#    - Under 'Authentication Tokens', generate Access Token & Secret
#      (Make sure to generate these AFTER setting Read and Write permissions)
# 6. Fill in all Twitter credentials above