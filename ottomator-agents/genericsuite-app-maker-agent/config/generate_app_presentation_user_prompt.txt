You are a ideation specialist, and entrepreneur that has a passion for creative thinking, identify opportunities, develop new products or services, and often start your own businesses.

I'm a developer more focused on technical aspects but not so much on business or creative thinking.

I need help from you to generate a presentation for a software application related to the application subject.

# Requirements

- The presentation should contain the following slides:

1. **Title Slide**:  
   - **Content**: `{title} and {subtitle}`
   - **Speaker Notes**: Introduce the presentation subject and provide context.  
   - **Prompt for Image**: "Generate an engaging title slide image that visually introduces the subject `{application_subject}`."

2. **Problem Statement**:  
   - **Content**: Describe the problem the application addresses. For instance, "Many people need real-time guidance for everyday tasks like cooking, DIY repairs, or setting up devices."  
   - **In this scenario**: `{problem_statement}`  
   - **Speaker Notes**: Explain why the problem is significant and how the audience might relate to it.  
   - **Prompt for Image**: "Create an image that depicts individuals facing a common problem that `{application_subject}` addresses."

3. **Objective**:  
   - **Content**: Explain the application's main goal. Example: "Provide users with step-by-step guidance through instructional videos and text for tasks they are unfamiliar with."  
   - **In this case**: `{objective}`  
   - **Speaker Notes**: Emphasize how the objective aligns with solving the problem introduced earlier.  
   - **Prompt for Image**: "Generate an illustration or icon set showcasing how `{application_subject}` helps users achieve their tasks."

4. **Technologies Used**:  
   - **Content**: Mention programming languages, AI models, API providers/platforms, etc. `{technologies_used}`  
   - **Speaker Notes**: Break down which technologies were used and why they were chosen for this project.  
   - **Prompt for Image**: "Create a visual representation or diagram showing the technology stack including languages, APIs, and tools used in `{application_subject}`."

5. **Application Features**:  
   - **Content**: Summary of the main features. `{application_features}`  
   - **Speaker Notes**: Provide more detailed explanations or anecdotes for each feature's utility.  
   - **Prompt for Image**: "Illustrate the primary features of `{application_subject}` using icons or short descriptions."

6. **How It Works**:
   - **Content**: Describe the workflow from user input to API integration and outputs. `{how_it_works}`  
   - **Speaker Notes**: Detail the process flow, ensuring the audience understands how each element contributes to the functionality.  
   - **Prompt for Image**: "Generate a flowchart or workflow diagram showing how `{application_subject}` processes user inputs and provides outputs."

7. **Screenshots**:  
   - **Content**: Include screenshots of the application showcasing key parts.  
   - **Speaker Notes**: Describe each screenshot's relevance, pointing out important functionalities.  
   - **Prompt for Image**: "Use the placeholders for screenshots of `{application_subject}` in use."

8. **Benefits**:
   - **Content**: Include the main benefits of the application. `{benefits}`  
   - **Speaker Notes**: Persuade the audience why these benefits help address the user's problem effectively.  
   - **Prompt for Image**: "Create an infographic or chart highlighting key benefits of using `{application_subject}` compared to manual methods or alternatives."

9. **Feedback and Future Development**:  
   - **Content**: Notable positive aspects and potential improvements `{feedback_and_future_development}`.  
   - **Speaker Notes**: Emphasize user satisfaction and iterate potential evolution based on feedback.  
   - **Prompt for Image**: "Create a slide image with text boxes or sticky notes representing customer feedback and future enhancements for `{application_subject}`."

10. **Future Vision**:  
    - **Content**: Describe possible enhancements to the application subject. Include possible use cases and future features.
    - **Speaker Notes**: Highlight the future possibilities of `{application_subject}` and how it can evolve to embrace users' needs.  
    - **Prompt for Image**: "Depict a futuristic or evolving version of `{application_subject}`, illustrating new possible features and enhanced capabilities."

11. **Thank You Slide**:  
    - **Content**: A final message thanking the audience for their interest in the application and possibly including a QR code for contact information.  
    - **Speaker Notes**: Express gratitude and briefly redirect users to follow-up links or next steps.  
    - **Prompt for Image**: "Design a thank you slide including a message of appreciation and space for a QR code. Ensure it's visually consistent with the rest of the presentation."

- The application subject is:
```
{application_subject}
```
