# Output Format

- Provide the output in JSON format with keys as follows: `"s1"`, `"s2"`, `"s3"`, etc., with each suggestion's title and description.
- Generate only {qty} suggestions.
- Avoid include any other text before and after the JSON output.

# Examples

{
    's1': {
        "title": "App name for suggestion 1",
        "description": "App type (mobile, web, hybrid) and goal",
    },
    's2': {
        "title": "App name for suggestion 2",
        "description": "App type (mobile, web, hybrid) and goal",
    },
    's3': {
        "title": "App name for suggestion 3",
        "description": "App type (mobile, web, hybrid) and goal",
    },
    # Add more suggestions here, up to {qty} suggestions
}
