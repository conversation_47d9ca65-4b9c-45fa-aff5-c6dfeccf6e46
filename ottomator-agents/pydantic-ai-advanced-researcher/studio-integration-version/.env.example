# Get your SUPABASE URL from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=

# Set this bearer token to whatever you want. This will be changed once the agent is hosted for you on the Studio!
API_BEARER_TOKEN=

# Get your Open AI API Key by following these instructions -
# https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# You only need this environment variable set if you are using GPT (and not Ollama)
OPENAI_API_KEY=

# The LLM you want to use. If this is set to any GPT model, it will use the OpenAI API.
# Otherwise it will assume you are using Ollama and will use the Ollama API.
LLM_MODEL=

# Get your Brave API key by going to the following link after signing up for Brave:
# https://api.search.brave.com/app/keys
BRAVE_API_KEY=